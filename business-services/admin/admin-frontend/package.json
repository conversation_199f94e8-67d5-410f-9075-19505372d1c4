{"name": "exchanges-admin-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write . --ignore-path .gitignore"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/icons-vue": "^7.0.0", "ant-design-vue": "^4.0.0", "axios": "^1.10.0", "dayjs": "^1.11.0", "echarts": "^5.4.0", "jsencrypt": "^3.3.2", "pinia": "^2.1.0", "puppeteer-core": "^24.13.0", "vue": "^3.3.0", "vue-i18n": "^12.0.0-alpha.2", "vue-router": "^4.2.0"}, "devDependencies": {"@eslint/css": "^0.10.0", "@eslint/js": "^9.32.0", "@eslint/json": "^0.13.1", "@eslint/markdown": "^7.1.0", "@types/node": "^20.0.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.8", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^9.0.0", "globals": "^16.3.0", "jiti": "^2.5.1", "less": "^4.4.0", "prettier": "^3.6.2", "sass": "^1.69.0", "typescript": "^5.0.0", "typescript-eslint": "^8.38.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}}