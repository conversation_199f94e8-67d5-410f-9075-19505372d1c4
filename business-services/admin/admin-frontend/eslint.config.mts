import pluginVue from 'eslint-plugin-vue'
import vueTsEslintConfig from '@vue/eslint-config-typescript'
import pluginPrettier from 'eslint-plugin-prettier'
import eslintConfigPrettier from 'eslint-config-prettier'

export default [
  {
    name: 'app-files',
    files: ['**/*.{ts,js,mjs,mts,cts,vue}'],
    languageOptions: {
      parserOptions: {
        parser: '@typescript-eslint/parser',
        ecmaVersion: 2020,
        sourceType: 'module'
      }
    },
    plugins: {
      'vue': pluginVue,
      'prettier': pluginPrettier
    },
    extends: [
      ...pluginVue.configs['flat/essential'],
      ...vueTsEslintConfig(),
      eslintConfigPrettier,
      pluginPrettier.configs['flat/recommended']
    ],
    rules: {
      'prettier/prettier': 'error',
      'vue/multi-word-component-names': 'off'
    }
  }
]