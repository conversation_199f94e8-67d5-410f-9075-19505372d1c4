import request from '@/utils/request';

export function preLogin(data: object) {
  return request.post("user/auth/login/pre",data);
}

export function googleLogin(data: object) {
  return request.post("user/auth/login/google",data);
}

export function validateSession(data: object) {
  return request.post("user/auth/session/verify",data);
}

export function getGoogleCodeTest(username: String) {
  return request.get("user/profile/gcode-test",{"username":username});
}


export function logout() {
  return request.post("user/auth/logout");
}