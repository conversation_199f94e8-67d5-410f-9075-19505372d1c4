import request from '@/utils/request'

export function getUserList(params: any) {
  return request.get('sys/user', params)
}

export function getUserDetail(id: number) {
  return request.get('sys/user/' + id)
}

export function addUser(data: object) {
  return request.post('sys/user', data)
}

export function updateUser(data: object) {
  return request.put('sys/user', data)
}

export function delUser(id: number) {
  return request.del('sys/user/' + id)
}
