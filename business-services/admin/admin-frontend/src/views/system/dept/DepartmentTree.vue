<template>
  <a-tree
    :tree-data="treeData"
    :field-names="fieldNames"
    :expandedKeys="expandedKeys"
    @update:expandedKeys="(val: any) => $emit('update:expandedKeys', val)"
    @select="onSelect"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDepartmentTree } from '@/api/system/department'

const props = defineProps({
  expandedKeys: {
    type: Array,
    default: () => [],
  },
  fieldNames: {
    type: Object,
    default: () => ({ title: 'name', key: 'id', children: 'children' }),
  },
  defaultExpandAll: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['select', 'update:expandedKeys'])

const treeData = ref<any[]>([])

function collectAllIds(tree: any[]): string[] {
  const ids: string[] = []
  function dfs(nodes: any[]) {
    nodes.forEach((node) => {
      ids.push(node.id)
      if (node.children && node.children.length) {
        dfs(node.children)
      }
    })
  }
  dfs(tree)
  return ids
}

const fetchTree = async () => {
  const res = await getDepartmentTree()
  treeData.value = res.data?.list || []
  if (props.defaultExpandAll) {
    emit('update:expandedKeys', collectAllIds(treeData.value))
  }
}

onMounted(fetchTree)

function onSelect(keys: any, e: any) {
  emit('select', keys, e)
}
</script>
