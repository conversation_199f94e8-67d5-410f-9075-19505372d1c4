<template>
  <div class="department-management">
    <a-card title="部门管理">
      <template #extra>
        <a-button type="primary" :loading="loading" @click="showAddModal">
          <Icon name="PlusOutlined" />
          新增部门
        </a-button>
      </template>

      <Table
        ref="tableRef"
        :dataSource="getDepartmentTree"
        :columns="columns"
        :showPagination="false"
        :loading="loading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <SwitchTag :status="record.status" />
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                type="link"
                :loading="loading"
                @click="showEditModal(record)"
                >编辑</a-button
              >
              <a-button
                type="link"
                :loading="loading"
                @click="showAddChildModal(record)"
                >新增下级</a-button
              >
              <a-popconfirm
                title="确定要删除吗？"
                @confirm="deleteDepartment(record)"
              >
                <a-button type="link" :loading="loading" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </Table>
    </a-card>

    <!-- 新增/编辑部门模态框 -->
    <DepartmentForm
      :formData="modalData"
      v-model:visible="modalVisible"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import SwitchTag from '@/components/tag/SwitchTag.vue'
import Icon from '@/components/icon/Icon.vue'
import Table from '@/components/table/Table.vue'
import {
  getDepartmentTree,
  deleteDepartment as deleteDepartmentApi,
} from '@/api/system/department'
import DepartmentForm from './DepartmentForm.vue'
import type { DepartmentFormType } from './DepartmentForm.vue'

// 类型定义
interface Department {
  id: number
  parentId: number | null
  name: string
  sort: number
  status: number
  children?: Department[]
  parentName?: string
}

const loading = ref(false)
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const modalData = ref<Partial<DepartmentFormType>>({})
const modalVisible = ref(false)

const showAddModal = () => {
  modalData.value = {}
  modalVisible.value = true
}

const showAddChildModal = (record: Department) => {
  modalData.value = {
    parentId: record.id,
  }
  modalVisible.value = true
}

const showEditModal = (record: Department) => {
  // 深拷贝记录以避免修改原始数据
  modalData.value = record
  modalData.value.parentId = record.parentId === 0 ? null : record.parentId
  modalVisible.value = true
}

// 表单操作成功
const handleFormSuccess = () => {
  tableRef.value?.refresh()
}

// 删除部门
const deleteDepartment = async (record: Department) => {
  loading.value = true
  try {
    await deleteDepartmentApi(record.id)
    message.success('操作成功')
    tableRef.value?.refresh()
  } catch (error) {
    message.error('操作失败')
  } finally {
    loading.value = false
  }
}

// 表格列配置
const columns = [
  { key: 'name', dataIndex: 'name', title: '部门名称' },
  { key: 'status', dataIndex: 'status', title: '状态' },
  { key: 'action', dataIndex: 'action', title: '操作' },
]
</script>

<style scoped>
.department-management {
  padding: 24px;
}
</style>
