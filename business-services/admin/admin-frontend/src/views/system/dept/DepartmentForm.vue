<template>
  <FormModal
    v-bind="$attrs"
    :initData="formInitData"
    :form-data="formData"
    :visible="visible"
    :rules="rules"
    :save-method="createDepartment"
    :update-method="updateDepartment"
  >
    <template #default="{ form }">
      <a-form-item label="上级部门" name="parentId">
        <DepartmentTreeSelect v-model:value="form.parentId" />
      </a-form-item>

      <a-form-item label="部门名称" name="name" required>
        <a-input v-model:value="form.name" showCount />
      </a-form-item>

      <a-form-item label="显示顺序" name="sort">
        <a-input-number v-model:value="form.sort" />
      </a-form-item>

      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="form.status" :options="statusOptions" />
      </a-form-item>
    </template>
  </FormModal>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form'
import FormModal from '@/components/modal/FormModal.vue'
import DepartmentTreeSelect from './DepartmentTreeSelect.vue'
import { createDepartment, updateDepartment } from '@/api/system/department'

// 定义表单数据类型
export interface DepartmentFormType {
  id: number | null
  parentId: number | null
  name: string
  sort: number
  status: number
}

// 定义从父组件接收的props
interface Props {
  visible: boolean
  formData: Partial<DepartmentFormType>
}

defineProps<Props>()

const formInitData: DepartmentFormType = {
  id: null,
  parentId: null,
  name: '',
  sort: 0,
  status: 0,
}

const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  sort: [
    {
      required: true,
      type: 'number',
      min: 0,
      max: 9999,
      message: '显示顺序必须为0-9999的整数',
      trigger: 'change',
    },
  ],
}

const statusOptions = [
  { label: '正常', value: 0 },
  { label: '停用', value: 1 },
]
</script>
