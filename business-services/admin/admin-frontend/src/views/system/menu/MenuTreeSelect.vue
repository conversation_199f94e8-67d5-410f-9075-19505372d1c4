<template>
  <a-tree-select
    :value="modelValue"
    @change="onChange"
    :tree-data="treeData"
    :field-names="{ label: 'name', value: 'id', children: 'children' }"
    placeholder="请选择"
    :loading="loading"
    style="width: 100%"
    allow-clear
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getMenuTree } from '@/api/system/menu'

interface Props {
  modelValue?: number | null
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
})

interface Emits {
  (e: 'update:modelValue', value: number | null): void
}
const emit = defineEmits<Emits>()

const menuTree = ref<any[]>([])
const loading = ref(false)

// 转换树形数据格式
const treeData = computed(() => {
  return filteMenu(menuTree.value)
})

// 转换数据格式以适配TreeSelect组件
function filteMenu(data: any[]): any[] {
  return data.filter((menu) => menu.type !== 3)
}
const onChange = (newValue: number | null) => {
  emit('update:modelValue', newValue)
}
// 获取菜单树数据
const fetchMenuTree = async () => {
  if (loading.value) return
  loading.value = true
  try {
    const res = await getMenuTree()
    menuTree.value = res.data.list || []
  } catch (error) {
    console.error('获取菜单树失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchMenuTree()
})
</script>
