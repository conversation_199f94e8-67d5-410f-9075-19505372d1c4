<template>
  <div class="menu-management">
    <a-card title="菜单管理">
      <template #extra>
        <a-button type="primary" :loading="loading" @click="showAddModal">
          <Icon name="PlusOutlined" />
          新增菜单
        </a-button>
      </template>

      <!-- 菜单表格 -->
      <Table
        ref="tableRef"
        :dataSource="menuList"
        :columns="columns"
        :showPagination="false"
        :loading="loading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">{{
              getTypeText(record.type)
            }}</a-tag>
          </template>
          <template v-else-if="column.key === 'status'">
            <SwitchTag :status="record.status" />
          </template>
          <template v-else-if="column.key === 'icon'">
            <Icon :name="record.icon" style="font-size: 18px" />
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                type="link"
                @click="showEditModal(record)"
                :loading="loading"
                >编辑</a-button
              >
              <a-button
                type="link"
                @click="showAddChildModal(record)"
                :loading="loading"
                >新增下级</a-button
              >
              <a-popconfirm
                title="确定要删除吗？"
                @confirm="deleteMenu(record)"
              >
                <a-button type="link" :loading="loading" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </Table>
    </a-card>

    <!-- 菜单表单子组件 -->
    <MenuForm
      :formData="modalData"
      v-model:visible="modalVisible"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { getMenuTree, deleteMenu as deleteMenuApi } from '@/api/system/menu'
import SwitchTag from '@/components/tag/SwitchTag.vue'
import Icon from '@/components/icon/Icon.vue'
import MenuForm from './MenuForm.vue'
import Table from '@/components/table/Table.vue'
import type { MenuFormType } from './MenuForm.vue'
// 响应式数据
const loading = ref(false)
const menuList = ref<any[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)

const modalData = ref<Partial<MenuFormType>>({})
const modalVisible = ref(false)
// 获取类型颜色
const getTypeColor = (type: number): string => {
  const colors: Record<number, string> = { 0: 'blue', 1: 'green', 2: 'orange' }
  return colors[type] ?? 'default'
}

// 获取类型文本
const getTypeText = (type: number) => {
  const texts: Record<number, string> = { 0: '目录', 1: '菜单', 2: '按钮' }
  return texts[type as keyof typeof texts] || '未知'
}

// 获取菜单列表（全部数据，树结构分组，默认展开所有层级）
const fetchMenuList = async () => {
  if (loading.value) return
  loading.value = true
  try {
    const res = await getMenuTree()
    menuList.value = res.data?.list || []
    updateMenuCache(menuList.value)
  } catch (error) {
    message.error('获取菜单列表失败')
  } finally {
    loading.value = false
  }
}

const showAddModal = () => {
  modalData.value = {}
  modalVisible.value = true
}

const showAddChildModal = (record: MenuFormType) => {
  modalData.value = {
    parentId: record.id,
  }
  modalVisible.value = true
}

const showEditModal = (record: MenuFormType) => {
  // 深拷贝记录以避免修改原始数据
  modalData.value = record
  modalData.value.parentId = record.parentId === 0 ? null : record.parentId
  modalVisible.value = true
}

// 表单操作成功
const handleFormSuccess = () => {
  // tableRef.value?.refresh()
  fetchMenuList() //刷新缓存
}

// 删除菜单
const deleteMenu = async (record: any) => {
  if (loading.value) return
  loading.value = true
  try {
    await deleteMenuApi(record.id)
    message.success('操作成功')
    loading.value = false
    fetchMenuList()
  } catch (error) {
    message.error('操作失败')
  } finally {
    loading.value = false
  }
}

// 表格列配置
const columns = [
  { title: '菜单名称', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '权限标识', dataIndex: 'permission', key: 'permission' },
  { title: '路由地址', dataIndex: 'path', key: 'path' },
  { title: '图标', dataIndex: 'icon', key: 'icon' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '操作', dataIndex: 'action', key: 'action' },
]

// 组件挂载时仅获取数据
onMounted(async () => {
  await fetchMenuList()
})

// 新增/编辑菜单后，更新本地缓存和页面数据
const updateMenuCache = (menuList: any[]) => {
  localStorage.setItem('menus', JSON.stringify(menuList))
}
</script>

<style scoped>
.menu-management {
  padding: 24px;
}
</style>
