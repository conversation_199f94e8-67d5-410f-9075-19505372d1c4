<template>
  <div class="role-management">
    <a-card title="角色管理">
      <template #extra>
        <a-button type="primary" @click="showAddModal">
          <template #icon>
            <PlusOutlined />
          </template>
          新增角色
        </a-button>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="角色名称">
            <a-input v-model:value="searchForm.name" placeholder="请输入角色名称" />
          </a-form-item>
          <a-form-item label="角色编码">
            <a-input v-model:value="searchForm.code" placeholder="请输入角色编码" />
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 120px">
              <a-select-option :value="true">启用</a-select-option>
              <a-select-option :value="false">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 角色表格 -->
      <a-table
        :columns="columns"
        :data-source="roleList"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status ? 'green' : 'red'">
              {{ record.status ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="editRole(record)">编辑</a-button>
              <a-button type="link" size="small" danger @click="deleteRole(record)">删除</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑角色模态框 -->
    <RoleForm
      :modalVisible="modalVisible"
      :isEdit="isEdit"
      :formData="formData"
      :actionLoading="actionLoading || undefined"
      :handleSubmit="handleSubmit"
      :handleCancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { getRoleList, createRole, updateRole, deleteRole as deleteRoleApi } from '@/api/system/role'

// 响应式数据
const loading = ref(false)
const roleList = ref([])
const modalVisible = ref(false)
const isEdit = ref(false)
const actionLoading = ref(false)

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  status: undefined
})

// 表单数据
const formData = reactive({
  id: null,
  code: '',
  name: '',
  desc: '',
  status: true
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '角色编码',
    dataIndex: 'code',
    key: 'code'
  },
  {
    title: '角色名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '角色描述',
    dataIndex: 'desc',
    key: 'desc'
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

// 获取角色列表
const fetchRoleList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize
    }
    const res = await getRoleList(params)
    roleList.value = res.data.list || []
    pagination.total = res.data.total || 0
    message.success('角色列表加载成功')
  } catch (error) {
    message.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchRoleList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'status' ? undefined : ''
  })
  handleSearch()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchRoleList()
}

// 显示新增模态框
const showAddModal = () => {
  isEdit.value = false
  resetFormData()
  modalVisible.value = true
}

// 编辑角色
const editRole = (record: any) => {
  isEdit.value = true
  Object.assign(formData, record)
  modalVisible.value = true
}

// 删除角色
const deleteRole = async (record: any) => {
  try {
    await deleteRoleApi(record.id)
    message.success('删除成功')
    fetchRoleList()
  } catch (error) {
    message.error('删除失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  actionLoading.value = true
  try {
    if (isEdit.value) {
      await updateRole(formData)
      message.success('更新成功')
    } else {
      await createRole(formData)
      message.success('创建成功')
    }
    modalVisible.value = false
    fetchRoleList()
  } catch (error) {
    message.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    actionLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetFormData()
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: null,
    code: '',
    name: '',
    desc: '',
    status: true
  })
}

// 组件挂载时获取数据
onMounted(() => {
  fetchRoleList()
})
</script>

<style scoped>
.role-management {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>