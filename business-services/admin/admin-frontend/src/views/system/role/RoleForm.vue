<template>
  <a-modal
    :open="modalVisible"
    :title="isEdit ? '编辑角色' : '新增角色'"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <a-form-item label="角色编码" required>
        <a-input v-model:value="formData.code" placeholder="请输入角色编码" />
      </a-form-item>
      <a-form-item label="角色名称" required>
        <a-input v-model:value="formData.name" placeholder="请输入角色名称" />
      </a-form-item>
      <a-form-item label="角色描述">
        <a-textarea v-model:value="formData.desc" placeholder="请输入角色描述" />
      </a-form-item>
      <a-form-item label="状态">
        <a-switch v-model:checked="formData.status" checked-children="启用" un-checked-children="禁用" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
const props = defineProps<{
  modalVisible: boolean
  isEdit: boolean
  formData: Record<string, any>
  actionLoading?: string
  handleSubmit: () => void
  handleCancel: () => void
}>()
</script>
