<template>
  <a-row :gutter="16">
    <a-col :span="5">
      <a-card :title="t('layout.depts')" style="height: 100%">
        <DepartmentTree
          :expandedKeys="deptExpandedKeys"
          :defaultExpandAll="true"
          @update:expandedKeys="(val: string[]) => (deptExpandedKeys = val)"
          @select="onDeptSelect"
        />
      </a-card>
    </a-col>
    <a-col :span="19">
      <a-card>
        <template #title>
          <a-space>
            <a-button
              type="primary"
              v-permission="'sys:user:create'"
              @click="showAddModal"
            >
              {{ t('common.add') }}
            </a-button>
            <Permission permission="sys:user:import">
              <a-button>{{ t('common.import') }}</a-button>
            </Permission>
            <a-button v-if="hasBtnPermission('sys:user:export')">
              {{ t('common.export') }}
            </a-button>
          </a-space>
        </template>
        <a-form layout="inline" @submit.prevent>
          <a-form-item>
            <a-input
              v-model:value="searchForm.username"
              :placeholder="t('user.username')"
            />
          </a-form-item>
          <a-form-item>
            <a-input
              v-model:value="searchForm.email"
              :placeholder="t('user.email')"
            />
          </a-form-item>
          <a-form-item>
            <a-input
              v-model:value="searchForm.phone"
              :placeholder="t('user.phone')"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="doSearch">{{
              t('common.search')
            }}</a-button>
          </a-form-item>
        </a-form>
        <Table
          ref="tableRef"
          :dataSource="getUserList"
          :columns="columns"
          :queryParams="searchForm"
          :loading="loading"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <UserStatusTag :status="record.status" />
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button
                  type="link"
                  v-permission="'sys:user:modify'"
                  @click="showEditModal(record)"
                >
                  {{ t('common.edit') }}
                </a-button>
                <Permission permission="sys:user:del">
                  <a-popconfirm
                    :title="t('common.confirmDelete')"
                    @confirm="handleDelUser(record.id)"
                  >
                    <a-button type="link" danger>{{
                      t('common.delete')
                    }}</a-button>
                  </a-popconfirm>
                </Permission>
              </a-space>
            </template>
          </template>
        </Table>
      </a-card>
      <UserForm
        :formData="modalData"
        v-model:visible="modalVisible"
        @success="doSearch"
      />
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import DepartmentTree from '@/views/system/dept/DepartmentTree.vue'
import UserStatusTag from '@/components/tag/UserStatusTag.vue'
import Permission from '@/components/Permission.vue'
import { getUserList, delUser } from '@/api/system/user'
import { usePermission } from '@/utils/permission'
import { message } from 'ant-design-vue'
import Table from '@/components/table/Table.vue'
import UserForm from './UserForm.vue'
import type { UserFormType } from './UserForm.vue'
const { t } = useI18n()
const { hasBtnPermission } = usePermission()

const modalData = ref<Partial<UserFormType>>({})
const modalVisible = ref(false)
const deptExpandedKeys = ref<string[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const loading = ref(false)

const searchForm = reactive({
  username: '',
  email: '',
  phone: '',
  deptId: undefined,
})

const columns = [
  { title: t('user.id'), dataIndex: 'id', key: 'id' },
  { title: t('user.username'), dataIndex: 'username', key: 'username' },
  { title: t('user.nickname'), dataIndex: 'nickname', key: 'nickname' },
  { title: t('user.email'), dataIndex: 'email', key: 'email' },
  { title: t('user.phone'), dataIndex: 'phone', key: 'phone' },
  { title: t('user.status'), dataIndex: 'status', key: 'status' },
  { title: t('user.createTime'), dataIndex: 'createTime', key: 'createTime' },
  { title: t('common.action'), key: 'action' },
]

const onDeptSelect = (selectedKeys: any[]) => {
  searchForm.deptId = selectedKeys[0]
  doSearch()
}

const showAddModal = () => {
  modalData.value = {}
  modalVisible.value = true
}

const showEditModal = (record: UserFormType) => {
  modalData.value = record
  modalVisible.value = true
}
const handleDelUser = async (id: number) => {
  try {
    await delUser(id)
    message.success(t('common.deleteSuccess'))
    doSearch()
  } catch (error) {
    message.error(t('common.deleteFail'))
  }
}
const doSearch = () => {
  tableRef.value?.refresh()
}
</script>
