<template>
  <FormModal
    v-bind="$attrs"
    :initData="formInitData"
    :form-data="formData"
    :visible="visible"
    :rules="rules"
    :save-method="addUser"
    :update-method="updateUser"
  >
    <template #default="{ isEdit, form }">
      <a-form-item label="部门" name="department">
        <DepartmentTreeSelect v-model:value="form.deptId" />
      </a-form-item>
      <a-form-item label="用户名" name="username">
        <a-input v-model:value="form.username" placeholder="请输入用户名" />
      </a-form-item>
      <a-form-item label="昵称" name="nickname">
        <a-input v-model:value="form.nickname" placeholder="请输入昵称" />
      </a-form-item>
      <a-form-item label="邮箱" name="email">
        <a-input v-model:value="form.email" placeholder="请输入邮箱" />
      </a-form-item>
      <a-form-item label="手机号" name="phone">
        <a-input v-model:value="form.phone" placeholder="请输入手机号" />
      </a-form-item>
      <a-form-item v-if="isEdit" label="登录密码" name="update_password">
        <InputPassword
          v-model:value="form.password"
          placeholder="留空则不更新密码"
        />
      </a-form-item>
      <a-form-item v-else label="登录密码" name="create_password">
        <InputPassword v-model:value="form.password" />
      </a-form-item>
    </template>
  </FormModal>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form'
import FormModal from '@/components/modal/FormModal.vue'
import { addUser, updateUser } from '@/api/system/user'
import { InputPassword } from 'ant-design-vue'
import DepartmentTreeSelect from '../dept/DepartmentTreeSelect.vue'
// 定义表单数据类型
export interface UserFormType {
  id: number | null
  username: string
  nickname: string
  email: string
  phone: string
  password: string
  status: number
}

// 定义从父组件接收的props
interface Props {
  visible: boolean
  formData: Partial<UserFormType>
}
defineProps<Props>()

const formInitData: UserFormType = {
  id: null,
  username: '',
  nickname: '',
  email: '',
  phone: '',
  password: '',
  status: 0,
}

const rules: Record<string, Rule[]> = {
  department: [
    {
      required: true,
      message: '请选择部门',
      trigger: 'change',
    },
  ],
  username: [
    {
      required: true,
      message: '请输入用户名',
      trigger: 'blur',
    },
    {
      pattern: /^[a-zA-Z0-9_-]{2,30}$/,
      message: '用户名格式不正确',
      trigger: 'blur',
    },
  ],
  update_password: [
    {
      min: 6,
      max: 20,
      message: '密码长度在 6 到 20 个字符',
      trigger: 'blur',
    },
  ],
  create_password: [
    {
      required: true,
      message: '请输入登录密码',
      trigger: 'blur',
    },
    {
      min: 6,
      max: 20,
      message: '密码长度在 6 到 20 个字符',
      trigger: 'blur',
    },
  ],
}
</script>
