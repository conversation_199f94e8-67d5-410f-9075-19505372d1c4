<template>
  <a-form :model="formModel" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
    <a-form-item label="用户名">
      <a-input v-model:value="formModel.username" disabled />
    </a-form-item>
    <a-form-item label="昵称">
      <a-input v-model:value="formModel.nickname" />
    </a-form-item>
    <a-form-item label="邮箱">
      <a-input v-model:value="formModel.email" />
    </a-form-item>
    <a-form-item label="手机号">
      <a-input v-model:value="formModel.phone" />
    </a-form-item>
    <slot />
  </a-form>
</template>

<script setup>
const props = defineProps({
  formModel: {
    type: Object,
    required: true
  }
})
</script>
