<template>
  <div class="dashboard">
    <a-card class="welcome-card">
      <h2>欢迎回来！</h2>
      <p>这是Venus Exchange管理后台系统</p>
      <a-row :gutter="16" style="margin-top: 24px">
        <a-col :span="8">
          <a-statistic title="今日访问" :value="1128" :value-style="{ color: '#3f8600' }">
            <template #prefix><ArrowUpOutlined /></template>
          </a-statistic>
        </a-col>
        <a-col :span="8">
          <a-statistic title="总用户数" :value="5678" :value-style="{ color: '#1890ff' }">
            <template #prefix><UserOutlined /></template>
          </a-statistic>
        </a-col>
        <a-col :span="8">
          <a-statistic title="系统状态" value="正常" :value-style="{ color: '#52c41a' }">
            <template #prefix><CheckCircleOutlined /></template>
          </a-statistic>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup>
import { ArrowUpOutlined, UserOutlined, CheckCircleOutlined } from '@ant-design/icons-vue'
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
  padding: 24px;
  width: 100%;
  background: #f0f2f5;
}
.welcome-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  width: 100%;
}
</style>
