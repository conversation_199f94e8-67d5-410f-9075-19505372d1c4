<template>
  <a-dropdown>
    <a-avatar style="background-color: #87d068" icon="user" />
    <template #overlay>
      <a-menu>
        <a-menu-item key="profile" @click="showProfileModal">个人信息</a-menu-item>
        <a-menu-item key="logout" @click="handleLogout">退出登录</a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
  <a-modal v-model:open="profileModalVisible" title="个人信息" @ok="handleProfileUpdate" @cancel="() => profileModalVisible = false" :confirmLoading="profileLoading">
    <UserProfileForm :formModel="profileForm" />
  </a-modal>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { logout } from '@/api/auth/auth';
import { getProfile, updateProfile } from '@/api/auth/profile';
import { message } from 'ant-design-vue';
import UserProfileForm from '@/views/profile/UserProfileForm.vue'
import { useUserStore } from '@/stores/user'

const router = useRouter();
const userStore = useUserStore()
const profileModalVisible = ref(false)
const profileLoading = ref(false)
const profileForm = ref({ username: '', nickname: '', email: '', phone: '' })

const showProfileModal = async () => {
  profileModalVisible.value = true
  profileLoading.value = true
  try {
    const res = await getProfile()
    Object.assign(profileForm.value, res.data)
  } finally {
    profileLoading.value = false
  }
}

const handleProfileUpdate = async () => {
  profileLoading.value = true
  try {
    await updateProfile(profileForm.value)
    profileModalVisible.value = false
    message.success('个人信息已更新')
  } catch {
    message.error('个人信息更新失败')
  } finally {
    profileLoading.value = false
  }
}

const handleLogout = async () => {
  logout().then(() => {
    userStore.logout()
    router.replace('/login')
  }).catch(() => {
    message.error('退出登录失败，请稍后再试')
  })
}
</script>
