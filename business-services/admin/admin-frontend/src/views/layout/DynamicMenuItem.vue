<template>
  <!-- 如果有子菜单，渲染为子菜单 -->
  <a-sub-menu v-if="hasChildren" :key="`${menu.id}`">
    <template #title>
      <component :is="getIcon(menu.icon)" v-if="menu.icon" />
      <span>{{ menu.name }}</span>
    </template>
    <template v-for="child in menu.children" :key="child.id">
      <DynamicMenuItem :menu="child" />
    </template>
  </a-sub-menu>
  
  <!-- 如果没有子菜单，渲染为菜单项 -->
  <a-menu-item v-else :key="`${menu.id}`">
    <component :is="getIcon(menu.icon)" v-if="menu.icon" />
    <span>{{ menu.name }}</span>
  </a-menu-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import * as Icons from '@ant-design/icons-vue'
import type { MenuInfo } from '@/stores/user'

interface Props {
  menu: MenuInfo
}

const props = defineProps<Props>()

// 检查是否有子菜单（排除按钮类型）
const hasChildren = computed(() => {
  return props.menu.children && 
         props.menu.children.length > 0 && 
         props.menu.children.some(child => child.type !== 2) // 2是按钮类型
})

// 获取图标组件
const getIcon = (iconName?: string) => {
  if (!iconName) return null
  
  // 处理常见的图标名称映射
  const iconMap: Record<string, string> = {
    'SettingOutlined': 'SettingOutlined',
    'DashboardOutlined': 'DashboardOutlined',
    'UserOutlined': 'UserOutlined',
    'TeamOutlined': 'TeamOutlined',
    'MenuOutlined': 'MenuOutlined',
    'ApartmentOutlined': 'ApartmentOutlined',
    'HomeOutlined': 'HomeOutlined',
    'TestOutlined': 'ExperimentOutlined'
  }
  
  const mappedIconName = iconMap[iconName] || iconName
  return (Icons as any)[mappedIconName] || null
}
</script>
