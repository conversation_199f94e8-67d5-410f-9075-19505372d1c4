<template>
  <a-layout style="min-height: 100vh">
    <!-- menu -->
    <a-layout-sider v-model:collapsed="collapsed" collapsible>
      <div class="logo" />
      <DynamicMenu :collapsed="collapsed" />
    </a-layout-sider>
    <!-- header -->
    <a-layout>
      <a-layout-header style="background: #fff; padding: 0; display: flex; justify-content: flex-end; align-items: center;">
        <a-space>
          <a-dropdown>
            <a class="ant-dropdown-link" @click.prevent>
              {{ locale === 'zh-CN' ? '中文' : 'English' }}
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item key="zh-CN" @click="changeLocale('zh-CN')">中文</a-menu-item>
                <a-menu-item key="en-US" @click="changeLocale('en-US')">English</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <UserProfileDropdown />
        </a-space>
      </a-layout-header>
      <a-layout-content style="margin: 16px">
        <div :style="{ padding: '24px', background: '#fff', minHeight: 'calc(100vh - 150px)' }">
          <NoPermission v-if="$route.path === '/403'" />
          <router-view v-else></router-view>
        </div>
      </a-layout-content>
      <a-modal v-model:open="profileModalVisible" title="个人信息" @ok="handleProfileUpdate" @cancel="() => profileModalVisible = false" :confirmLoading="profileLoading">
        <a-form :model="profileForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
          <a-form-item label="用户名">
            <a-input v-model:value="profileForm.username" disabled />
          </a-form-item>
          <a-form-item label="昵称">
            <a-input v-model:value="profileForm.nickname" />
          </a-form-item>
          <a-form-item label="邮箱">
            <a-input v-model:value="profileForm.email" />
          </a-form-item>
          <a-form-item label="手机号">
            <a-input v-model:value="profileForm.phone" />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-layout>
  </a-layout>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { logout } from '@/api/auth/auth';
import { getProfile, updateProfile } from '@/api/auth/profile';
import { message } from 'ant-design-vue';
import UserProfileDropdown from './UserProfileDropdown.vue';
import DynamicMenu from './DynamicMenu.vue';
import NoPermission from '@/views/error/NoPermission.vue';


const collapsed = ref<boolean>(false);
const { t, locale } = useI18n();
const router = useRouter();
const changeLocale = (lang: string) => {
  locale.value = lang;
};

// 移除原有的菜单点击处理，现在由DynamicMenu组件处理

// 用户信息管理
const profileModalVisible = ref(false)
const profileLoading = ref(false)
const profileForm = ref({ username: '', nickname: '', email: '', phone: '' })

const showProfileModal = async () => {
  profileModalVisible.value = true
  profileLoading.value = true
  try {
    const res = await getProfile()
    Object.assign(profileForm.value, res.data)
  } finally {
    profileLoading.value = false
  }
}

const handleProfileUpdate = async () => {
  profileLoading.value = true
  try {
    await updateProfile(profileForm.value)
    profileModalVisible.value = false
    message.success('个人信息已更新')
  } catch {
    message.error('个人信息更新失败')
  } finally {
    profileLoading.value = false
  }
}

const handleLogout = async () => {
  await logout()
  router.replace('/login')
}
</script>

<style scoped>
.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
}
</style>