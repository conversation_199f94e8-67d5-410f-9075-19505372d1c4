/* 通用全局样式，可按需扩展 */

:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --border-radius: 6px;
  --box-padding: 24px;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  background: #f5f6fa;
  color: #222;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.card-box {
  background: #fff;
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px #f0f1f2;
  padding: var(--box-padding);
}

.mt-16 { margin-top: 16px; }
.mb-16 { margin-bottom: 16px; }
.ml-16 { margin-left: 16px; }
.mr-16 { margin-right: 16px; }

/* 按钮通用样式 */
.btn-primary {
  background: var(--primary-color);
  color: #fff;
  border-radius: var(--border-radius);
}

/* 表格通用样式 */
.table-striped tbody tr:nth-child(odd) {
  background: #fafbfc;
}
