import axios, { AxiosRequestConfig } from 'axios';
import { message } from 'ant-design-vue';
import { useUserStore } from '@/stores/user';
import router from '@/router';

interface ResponseBody {
  code: string;
  data: any;
  msg: string;
}


const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

service.interceptors.request.use(
  (config) => {
    const userStore = useUserStore();
    if (userStore.sessionId) {
      config.headers['Venus-Session'] = userStore.sessionId;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

service.interceptors.response.use(
  (response) => {
    // 自动保存 Venus-Session 到 userStore 和 localStorage
    let venusSession = response.headers['venus-session'] || response.headers['Venus-Session'];
    if (!venusSession) {
      const sessionKey = Object.keys(response.headers).find(k => k.toLowerCase() === 'venus-session');
      if (sessionKey) {
        venusSession = response.headers[sessionKey];
      }
    }

    if (venusSession) {
      const userStore = useUserStore();
      userStore.setSessionId(venusSession);
      localStorage.setItem('sessionId', venusSession);
    }
    const res = response.data;
    if (res.code !== '0') {
      message.error(res.msg || 'Error');
      return Promise.reject(new Error(res.msg || 'Error'));
    }
    return res;
  },
  async (error) => {
    const { response } = error;
    const userStore = useUserStore();
    const currentPath = window.location.pathname;
    if (response) {
      if (response.status === 401) {
        userStore.setSessionId('');
        userStore.userInfo = null;
        localStorage.removeItem('sessionId');
        message.error('登录已失效，请重新登录');
        if (currentPath !== '/login') {
          await router.replace('/login');
        }
        return Promise.reject(error);
      }
      if (response.status === 403) {
        message.error('无资源权限');
        if (currentPath !== '/403') {
          await router.replace('/403');
        }
        return Promise.reject(error);
      }
      message.error(response.data?.msg || error.message);
    } else {
      message.error(error.message);
    }
    return Promise.reject(error);
  }
);

export function get(url: string, query?: object) {
  return service.get(url, { params: query });
}

export function post(url: string, body?: object, query?: object) {
  return service.post(url, body, { params: query });
}

export function put(url: string, body?: object) {
  return service.put(url, body, { params: {} });
}

export function del(url: string) {
  return service.delete(url, { params: {} });
}

const request = {
  get,
  post,
  put,
  del
};

export default request;