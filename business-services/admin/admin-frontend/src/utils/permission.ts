import { useUserStore } from '@/stores/user'

/**
 * 权限工具类
 */
export class PermissionUtils {
  private static userStore = useUserStore()

  /**
   * 检查是否有按钮权限
   * @param permission 权限标识
   * @returns 是否有权限
   */
  static hasBtnPermission(permission: string): boolean {
    return this.userStore.hasBtnPermission(permission)
  }

  /**
   * 检查是否有多个权限中的任意一个
   * @param permissions 权限标识数组
   * @returns 是否有权限
   */
  static hasAnyBtnPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasBtnPermission(permission))
  }

  /**
   * 检查是否有多个权限中的所有权限
   * @param permissions 权限标识数组
   * @returns 是否有权限
   */
  static hasAllBtnPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.hasBtnPermission(permission))
  }

  /**
   * 检查是否是超级管理员（拥有所有权限）
   * @returns 是否是超级管理员
   */
  static isSuperAdmin(): boolean {
    return this.userStore.btnPerms.includes('*')
  }

  /**
   * 获取当前用户的所有按钮权限
   * @returns 权限数组
   */
  static getBtnPermissions(): string[] {
    return this.userStore.btnPerms
  }

  /**
   * 检查菜单是否可见
   * @param menuPermission 菜单权限标识
   * @returns 是否可见
   */
  static isMenuVisible(menuPermission?: string): boolean {
    if (!menuPermission) return true
    return this.hasBtnPermission(menuPermission)
  }
}

/**
 * 权限检查的组合式函数
 */
export function usePermission() {
  const userStore = useUserStore()

  const hasBtnPermission = (permission: string): boolean => {
    return userStore.hasBtnPermission(permission)
  }

  const hasAnyBtnPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasBtnPermission(permission))
  }

  const hasAllBtnPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasBtnPermission(permission))
  }

  const isSuperAdmin = (): boolean => {
    return userStore.btnPerms.includes('*')
  }

  const getBtnPermissions = (): string[] => {
    return userStore.btnPerms
  }

  const isMenuVisible = (menuPermission?: string): boolean => {
    if (!menuPermission) return true
    return hasBtnPermission(menuPermission)
  }

  return {
    hasBtnPermission,
    hasAnyBtnPermission,
    hasAllBtnPermissions,
    isSuperAdmin,
    getBtnPermissions,
    isMenuVisible
  }
}

export default PermissionUtils
