import type { Directive, DirectiveBinding } from 'vue'
import { useUserStore } from '@/stores/user'

/**
 * 权限指令
 * 使用方式：v-permission="'sys:user:create'"
 */
export const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const userStore = useUserStore()

    if (value) {
      const hasPermission = userStore.hasBtnPermission(value)
      if (!hasPermission) {
        el.style.display = 'none'
      }
    }
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const userStore = useUserStore()

    if (value) {
      const hasPermission = userStore.hasBtnPermission(value)
      if (!hasPermission) {
        el.style.display = 'none'
      } else {
        el.style.display = ''
      }
    }
  }
}

/**
 * 权限指令（移除元素）
 * 使用方式：v-permission-remove="'sys:user:create'"
 */
export const permissionRemove: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const userStore = useUserStore()

    if (value) {
      const hasPermission = userStore.hasBtnPermission(value)
      if (!hasPermission) {
        el.remove()
      }
    }
  }
}

export default {
  permission,
  permissionRemove
}
