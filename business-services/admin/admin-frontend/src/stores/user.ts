import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { RouteRecordRaw } from 'vue-router';
import { getViewComponent } from '@/router';

export interface UserInfo {
  id: number;
  username: string;
  nickname: string;
  email: string;
  phone: string;
  deptId: number;
  status: number;
  loginIp: string;
  createTime: string;
  updateTime: string;
  [key: string]: any;
}

export interface MenuInfo {
  id: number;
  name: string;
  permission?: string;
  type: number; // 1目录 2菜单 3按钮
  sort: number;
  parentId?: number;
  path?: string;
  icon?: string;
  component?: string;
  status: boolean;
  children?: MenuInfo[];
}

export interface LoginResponse {
  preToken?: string;
  userInfo: UserInfo;
  menus: MenuInfo[];
  btnPerms: string[];
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null);
  const sessionId = ref<string>('');
  const menus = ref<MenuInfo[]>([]);
  const btnPerms = ref<string[]>([]);
  const dynamicRoutes = ref<RouteRecordRaw[]>([]);
  const isLoggedIn = computed(() => !!sessionId.value && !!userInfo.value);

  // 从localStorage恢复状态
  const restoreFromStorage = () => {
    const storedSessionId = localStorage.getItem('sessionId');
    const storedUserInfo = localStorage.getItem('userInfo');
    const storedMenus = localStorage.getItem('menus');
    const storedBtnPerms = localStorage.getItem('btnPerms');
    const storedDynamicRoutes = localStorage.getItem('dynamicRoutes');

    if (storedSessionId) {
      sessionId.value = storedSessionId;
    }
    if (storedUserInfo) {
      try {
        userInfo.value = JSON.parse(storedUserInfo);
      } catch (error) {
        console.error('解析用户信息失败:', error);
      }
    }
    if (storedMenus) {
      try {
        menus.value = JSON.parse(storedMenus);
      } catch (error) {
        console.error('解析菜单信息失败:', error);
      }
    }
    if (storedBtnPerms) {
      try {
        btnPerms.value = JSON.parse(storedBtnPerms);
      } catch (error) {
        console.error('解析按钮权限失败:', error);
      }
    }
    if(storedDynamicRoutes){
      // 修改：从存储的数据重建路由，而不是直接解析
      try {
        const routesData = JSON.parse(storedDynamicRoutes);
        dynamicRoutes.value = rebuildRoutes(routesData);
      } catch (error) {
        console.error('解析动态路由失败:', error);
      }
    }
  };

  // 设置用户信息
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info;
    localStorage.setItem('userInfo', JSON.stringify(info));
  };

  // 设置Session ID
  const setSessionId = (id: string) => {
    sessionId.value = id;
    localStorage.setItem('sessionId', id);
  };

  // 设置菜单
  const setMenus = (menuList: MenuInfo[]) => {
    menus.value = menuList;
    localStorage.setItem('menus', JSON.stringify(menuList));
  };

  // 设置按钮权限
  const setBtnPerms = (perms: string[]) => {
    btnPerms.value = perms;
    localStorage.setItem('btnPerms', JSON.stringify(perms));
  };

    // 设置动态路由
  const setDynamicRoutes = (routes: RouteRecordRaw[]) => {
    dynamicRoutes.value = routes;
    // 修改：只存储路由的可序列化部分
    localStorage.setItem('dynamicRoutes', JSON.stringify(routes));
  };

  const getDynamicRoutes = (): RouteRecordRaw[] => {
    return dynamicRoutes.value || [];
  };
  
  // 从存储的数据重建路由
  const rebuildRoutes = (routesData: any[]): RouteRecordRaw[] => {
    return routesData.map(data => ({
      path: data.path,
      name: data.name,
      meta: data.meta,
      redirect: data.redirect,
      children: data.children ? rebuildRoutes(data.children) : undefined,
      // 从meta信息中重建组件
      component: data.meta?.componentPath ? getViewComponent(data.meta.componentPath) : undefined
    })) as RouteRecordRaw[];
  };

  // 生成动态路由
  const generateRoutes = (menuList: MenuInfo[]): RouteRecordRaw[] => {
    const routes: RouteRecordRaw[] = [];

    // 递归转换菜单为路由，自动动态 import 组件
    const mapMenuToRoute = (menu: MenuInfo): RouteRecordRaw | null =>  {
      // 只处理目录和菜单类型，跳过按钮
      if (menu.type === 2) return null; 

      const route: RouteRecordRaw = {
        path: menu.path || `/${menu.id}`,
        name: menu.component || `menu_${menu.id}`,
        meta: {
          title: menu.name,
          icon: menu.icon,
          permission: menu.permission,
          // 修改：将组件路径存储在meta中
          componentPath: menu.component
        },
        redirect: undefined,
        children: [],
        // 使用单个组件而不是 components 对象
        component: getViewComponent(menu.component),
      };

      // 处理子菜单
      if (menu.children && menu.children.length > 0) {
        const children: RouteRecordRaw[] = [];
        menu.children.forEach(child => {
          const childRoute = mapMenuToRoute(child);
          if (childRoute) {
            children.push(childRoute);
          }
        });
        route.children = children as RouteRecordRaw[]; // 临时解决方案，将类型断言为any
      }

      return route;
    }

    menuList.forEach(menu => {
      const route = mapMenuToRoute(menu);
      if (route) {
        routes.push(route);
      }
    });
    return routes;
  };

  // 登录
  const login = (loginResponse: LoginResponse) => {
    setUserInfo(loginResponse.userInfo);
    if (loginResponse.btnPerms) {
      setBtnPerms(loginResponse.btnPerms);
    }
    if (loginResponse.menus) {
      setMenus(loginResponse.menus);
      // 生成动态路由
      const routes = generateRoutes(loginResponse.menus);
      setDynamicRoutes(routes);
    }
    if (loginResponse.btnPerms) {
      setBtnPerms(Array.from(loginResponse.btnPerms));
    }
  };

  // 登出
  const logout = () => {
    userInfo.value = null;
    sessionId.value = '';
    menus.value = [];
    btnPerms.value = [];
    dynamicRoutes.value = [];
    localStorage.removeItem('sessionId');
    localStorage.removeItem('userInfo');
    localStorage.removeItem('permissions');
    localStorage.removeItem('menus');
    localStorage.removeItem('btnPerms');
    localStorage.removeItem('dynamicRoutes');
  };

  // 检查是否有按钮权限
  const hasBtnPermission = (permission: string) => {
    // 如果权限集合中包含 * 则直接返回 true
    if (btnPerms.value.includes('*')) {
      return true;
    }
    return btnPerms.value.includes(permission);
  };
  // 检查令牌是否有效
  const isTokenValid = () => {
    return !!sessionId.value;
  };

  restoreFromStorage();

  return {
    userInfo,
    sessionId,
    menus,
    btnPerms,
    dynamicRoutes,
    isLoggedIn,
    setUserInfo,
    setSessionId,
    setMenus,
    setBtnPerms,
    setDynamicRoutes,
    getDynamicRoutes,
    generateRoutes,
    login,
    logout,
    hasBtnPermission,
    isTokenValid,
    restoreFromStorage,
  };
});