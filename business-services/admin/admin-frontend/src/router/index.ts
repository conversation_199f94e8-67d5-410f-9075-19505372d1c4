import { RouteRecordRaw,createRouter, createWebHistory } from 'vue-router'

export interface MenuInfo {
  id: number;
  name: string;
  permission?: string;
  type: number; // 1目录 2菜单 3按钮
  sort: number;
  parentId?: number;
  path?: string;
  icon?: string;
  component?: string;
  status: boolean;
  children?: MenuInfo[];
}

// 预加载所有视图组件
const modules = import.meta.glob('@/views/**/index.vue')
// 获取组件的辅助函数
export function getViewComponent(componentPath: string | undefined) {
  if (!componentPath) {
    return undefined; // 目录
  }

  // 处理不同的路径格式
  let fullPath = ''
  if (componentPath.endsWith('.vue')) {
    fullPath = `/src/views/${componentPath}`
  } else {
    // 对于只指定组件名的情况，尝试在根目录查找
    fullPath = `/src/views/${componentPath}/index.vue`
  }
  // 如果找不到对应组件，使用 NotFound 组件
  return modules[fullPath] || (() => import('@/views/error/NotFound.vue'))
}

// 基础路由（layout 下包含 dashboard，动态业务 children 后续动态注册）
const routes = [
  {
    path: '/',
    name: 'layout',
    component: () => import('@/views/layout/Layout.vue'),
    redirect: '/dashboard',
    children: [
      // {
      //   path: 'dashboard',
      //   name: 'dashboard',
      //   component: () => import('@/views/dashboard/Dashboard.vue')
      // },
      {
        path: '403',
        name: 'error403',
        component: () => import('@/views/error/403.vue'),
        meta: { title: '无权限' }
      },
      {
        path: ':pathMatch(.*)*',
        name: 'notFound',
        component: () => import('@/views/error/NotFound.vue'),
        meta: { title: '页面未找到' }
      }
      // 动态业务路由将通过 setupDynamicRoutes 动态添加
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/auth/Login.vue')
  },
  // 403、404 已作为 layout 的 children，无需在外层重复
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 使用统一的路由守卫
import { setupRouterGuards } from './guards'
setupRouterGuards(router);

// 动态路由注册方法（需在登录后调用）
export function setupDynamicRoutes(menuList: MenuInfo[]) {
  // 递归转换菜单为路由，自动动态 import 组件
  const mapMenuToRoute = (menu: MenuInfo): RouteRecordRaw | null =>  {
    // 只处理目录和菜单类型，跳过按钮
    if (menu.type !== 1) return null;

    const route: RouteRecordRaw = {
      path: menu.path || `/${menu.id}`,
      name: menu.component || `menu_${menu.id}`,
      meta: {
        title: menu.name,
        icon: menu.icon,
        permission: menu.permission
      },
      redirect: undefined,
      children: [],
      components: {default: () => import(`@/views/${menu.component}.vue`)},
    };

    // 处理子菜单
    if (menu.children && menu.children.length > 0) {
      const children: RouteRecordRaw[] = [];
      menu.children.forEach(child => {
        const childRoute = mapMenuToRoute(child);
        if (childRoute) {
          children.push(childRoute);
        }
      });
      route.children = children as RouteRecordRaw[]; // 临时解决方案，将类型断言为any
    }

    return route;
  }

  menuList.forEach(menu => {
    const route = mapMenuToRoute(menu)
    console.log("添加动态路由："+JSON.stringify(route))
    if (route) {
      router.addRoute('layout', route);
    }
  })
}

export default router