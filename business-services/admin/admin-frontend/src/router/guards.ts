import type { Router } from 'vue-router';
import { useUserStore } from '@/stores/user';

const WHITE_LIST = ['/login'];

export function setupRouterGuards(router: Router) {
  // 路由注册状态（使用内存变量，这样刷新页面时会重置）
  let dynamicRoutesAdded = false;

  // 清理动态路由，保留基础路由
  const removeExistingRoutes = () => {
    const preservedRoutes = ['layout', 'login', 'error403', 'notFound'];
    router.getRoutes()
      .filter(route => !preservedRoutes.includes(route.name as string))
      .forEach(route => {
        if (route.name) {
          router.removeRoute(route.name);
        }
      });
    dynamicRoutesAdded = false;
  };

  // 刷新页面时重置路由状态
  removeExistingRoutes();

  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore();
    const sessionId = localStorage.getItem('sessionId') || '';
    
    if (sessionId) {
      try {
        // 1. 恢复用户状态
        if (!userStore.isLoggedIn) {
          await userStore.restoreFromStorage();
        }

        // 2. 获取并注册动态路由
        const routes = userStore.getDynamicRoutes() || [];
        if (!dynamicRoutesAdded) {
          // 重置路由
          removeExistingRoutes();

          // 注册新路由
          routes.forEach(route => {
            router.addRoute('layout', route);
          });

          dynamicRoutesAdded = true;

          // 重要：重新导航以激活新路由
          next({ path: to.fullPath, replace: true });
          return;
        }

        // 3. 路由权限检查
        if (to.meta?.permission && !userStore.hasBtnPermission(to.meta.permission as string)) {
          console.log('无权限访问:', to.path);
          next('/403');
          return;
        }

        // 4. 路由跳转处理
        if (to.path === '/login') {
          next({ path: '/dashboard' });
        } else if (to.path === '/' || to.path === '') {
          next({ path: '/dashboard' });
        } else {
          // 如果路由未匹配但动态路由已注册完成，说明是无效路由
          if (to.matched.length === 0 && dynamicRoutesAdded) {
            console.log('访问的路由不存在:', to.path);
            next('/404');
          } else {
            next();
          }
        }
      } catch (error) {
        console.error('路由处理失败:', error);
        removeExistingRoutes();
        next('/login');
      }
    } else {
      if (WHITE_LIST.includes(to.path)) {
        next();
      } else {
        next({ path: '/login' });
      }
    }
  });

  // 登出时清理路由
  router.afterEach((to) => {
    if (to.path === '/login') {
      removeExistingRoutes();
    }
  });
}