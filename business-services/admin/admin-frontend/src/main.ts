import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'

import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import { messages } from './locale';
import permissionDirectives from './directives/permission';
import '@/styles/global.less'

const i18n = createI18n({
  legacy: false,
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages,
});

const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(Antd)

// 注册权限指令
app.directive('permission', permissionDirectives.permission)
app.directive('permission-remove', permissionDirectives.permissionRemove)

// 挂载应用
app.mount('#app')