<template>
  <div class="icon-select-dropdown">
    <a-popover
      placement="bottomLeft"
      trigger="click"
      :open="popoverOpen"
      @openChange="onPopoverChange"
    >
      <template #content>
        <div class="icon-select-panel">
          <a-input v-model:value="search" placeholder="搜索图标" allow-clear size="small" style="margin-bottom: 8px;" />
          <div class="icon-list">
            <div
              v-for="icon in filteredIcons"
              :key="icon.name"
              class="icon-item"
              :class="{ selected: icon.name === modelValue }"
              @click="selectIcon(icon.name)"
            >
              <component :is="icon.component" style="font-size: 22px" />
            </div>
          </div>
        </div>
      </template>
      <a-button @click="popoverOpen = true" style="display: flex; align-items: center;">
        <component v-if="modelValue" :is="iconMap[modelValue]" style="font-size: 20px; margin-right: 6px;" />
        <span v-else style="color: #bbb; margin-right: 6px;">请选择图标</span>
        <DownOutlined />
      </a-button>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed,  defineEmits } from 'vue'
import * as icons from '@ant-design/icons-vue'
import { DownOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  modelValue: String
})
const emit = defineEmits(['update:modelValue'])

const search = ref('')
const popoverOpen = ref(false)

const iconList = Object.keys(icons)
  .filter(name => name.endsWith('Outlined'))
  .map(name => ({
    name,
    component: (icons as any)[name]
  }))

const iconMap = Object.fromEntries(iconList.map(i => [i.name, i.component]))

const filteredIcons = computed(() => {
  if (!search.value) return iconList
  return iconList.filter(icon => icon.name.toLowerCase().includes(search.value.toLowerCase()))
})

function selectIcon(name: string) {
  emit('update:modelValue', name)
  popoverOpen.value = false
}
function onPopoverChange(val: boolean) {
  popoverOpen.value = val
  if (!val) search.value = ''
}
</script>

<style scoped>
.icon-select-dropdown {
  display: inline-block;
}
.icon-select-panel {
  width: 320px;
  max-height: 260px;
  overflow-y: auto;
  padding: 4px 2px 2px 2px;
}
.icon-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.icon-item {
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  cursor: pointer;
  border: 1.5px solid transparent;
  transition: border-color 0.2s, background 0.2s;
  background: #f7f8fa;
}
.icon-item.selected {
  border-color: #1890ff;
  background: #e6f7ff;
}
.icon-item:hover {
  border-color: #91d5ff;
  background: #e6f7ff;
}
</style>
