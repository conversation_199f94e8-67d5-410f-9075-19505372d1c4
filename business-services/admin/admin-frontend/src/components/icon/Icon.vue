<template>
  <component :is="iconComponent" v-if="iconComponent" />
  <span v-else>-</span>
</template>

<script setup lang="ts">
import * as AntdIcons from '@ant-design/icons-vue';
import { computed } from 'vue';
import type { FunctionalComponent } from 'vue';

const props = defineProps({
  name: {
    type: String,
    required: true
  }
});

const iconComponent = computed(() => {
  const icon = AntdIcons[props.name as keyof typeof AntdIcons];
  return icon ? (icon as FunctionalComponent) : null;
});
</script>