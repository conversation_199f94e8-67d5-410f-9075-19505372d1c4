<template>
  <a-modal
    v-bind="$attrs"
    :open="visible"
    :title="isEdit ? '编辑' : '新增'"
    :maskClosable="false"
    @ok="handleSubmit"
    @cancel="closeModal"
    @close="closeModal"
    :confirmLoading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <slot :isEdit="isEdit" :form="form"></slot>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onBeforeUnmount } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

interface FormModalProps<T> {
  /** 表单初始化数据*/
  initData?: T
  /** 表单数据 */
  formData: Partial<T>
  /** 表单校验规则 */
  rules?: any
  /** 模态框可见性 */
  visible: boolean
  /** 新增方法 */
  saveMethod?: (data: T) => Promise<any>
  /** 编辑方法 */
  updateMethod?: (data: T) => Promise<any>
}

const props = withDefaults(defineProps<FormModalProps<any>>(), {
  initData: {},
  rules: {},
})

// 组件事件
const emit = defineEmits<{
  (e: 'success'): void
  (e: 'update:visible', value: boolean): void
}>()

const formRef = ref<FormInstance>()
const confirmLoading = ref(false)

const form = reactive<any>({})

// 编辑状态判断
const isEdit = computed(() => !!form.id)

// 监听可见性变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 模态框打开时，使用深拷贝确保每次都创建新的数据副本
      Object.assign(form, props.initData, props.formData)
    }
  },
)

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {}
}

// 表单验证
const validateForm = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

// 处理表单提交
const handleSubmit = async () => {
  // 表单验证
  if (!(await validateForm())) {
    return
  }

  confirmLoading.value = true
  try {
    const submitData = { ...form }
    if (isEdit.value) {
      props.updateMethod
        ? await props.updateMethod(submitData)
        : console.log('Update mehod undefined')
    } else {
      props.saveMethod
        ? await props.saveMethod(submitData)
        : console.log('Save mehod undefined')
    }
    message.success('操作成功')
    emit('success')
    closeModal()
  } catch (error) {
    message.error('操作失败')
  } finally {
    confirmLoading.value = false
  }
}

// 关闭模态框
const closeModal = () => {
  resetForm()
  emit('update:visible', false)
}

// 组件销毁前清理
onBeforeUnmount(() => {
  resetForm()
})
</script>

<style scoped>
:deep(.ant-modal-content) {
  padding: 0;
}

:deep(.ant-modal-header) {
  margin-bottom: 8px;
  padding: 16px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

:deep(.ant-modal-body) {
  padding: 24px;
  max-height: calc(100vh - 280px);
  overflow-y: auto;
}

:deep(.ant-modal-footer) {
  margin-top: 8px;
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}
</style>
