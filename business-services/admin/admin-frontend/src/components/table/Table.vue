<template>
  <div class="table-wrapper">
    <a-table
      v-bind="$attrs"
      :data-source="data"
      :loading="loading"
      :pagination="showPagination ? paginationConfig : false"
      @change="handleTableChange"
    >
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import type { TablePaginationConfig } from 'ant-design-vue'
import type {
  FilterValue,
  SorterResult,
} from 'ant-design-vue/es/table/interface'
import type { AxiosResponse } from 'axios'

interface TableProps {
  // API 请求函数或静态数据列表
  dataSource?: any[] | ((params: any) => Promise<AxiosResponse<any, any>>)
  // 是否显示分页
  showPagination?: boolean
  // 自定义查询参数
  queryParams?: Record<string, any>
  // 默认分页大小
  defaultPageSize?: number
  // 是否自动加载数据
  autoLoad?: boolean
}

// 组件属性定义
const props = withDefaults(defineProps<TableProps>(), {
  showPagination: true,
  defaultPageSize: 10,
  autoLoad: true,
})

// 组件事件
const emit = defineEmits(['update:loading', 'dataLoaded', 'error'])

// 自动判断数据源类型
const isApiMode = computed(() => typeof props.dataSource === 'function')

// 响应式状态
const loading = ref(false)
const data = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(props.defaultPageSize)
const sorter = ref<SorterResult | null>(null)
const filters = ref<Record<string, FilterValue | null>>({})

// 分页配置
const paginationConfig = ref<TablePaginationConfig>({
  total: 0,
  current: 1,
  pageSize: props.defaultPageSize,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条`,
})

// 处理表格变化（分页、排序、筛选）
const handleTableChange = (
  pagination: TablePaginationConfig,
  newFilters: Record<string, FilterValue | null>,
  newSorter: SorterResult,
) => {
  currentPage.value = pagination.current || 1
  pageSize.value = pagination.pageSize || props.defaultPageSize
  sorter.value = newSorter
  filters.value = newFilters

  isApiMode.value ? fetchTableData() : handleListData()
}

// 处理本地数据
const handleListData = () => {
  if (!Array.isArray(props.dataSource)) return

  let result = [...props.dataSource]

  // 应用过滤
  Object.keys(filters.value).forEach((key) => {
    const filterValues = filters.value[key]
    if (filterValues && filterValues.length) {
      result = result.filter((item) => filterValues.includes(item[key]))
    }
  })

  // 应用排序
  if (sorter.value && sorter.value.columnKey) {
    const { columnKey, order } = sorter.value
    result.sort((a, b) => {
      const compareResult = a[columnKey] > b[columnKey] ? 1 : -1
      return order === 'ascend' ? compareResult : -compareResult
    })
  }

  // 应用分页
  total.value = result.length
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  data.value = result.slice(start, end)

  updatePagination()
}

// API 模式获取数据
const fetchTableData = async () => {
  if (!isApiMode.value) return
  const fetchFn = props.dataSource as (
    params: any,
  ) => Promise<AxiosResponse<any, any>>

  loading.value = true
  emit('update:loading', true)

  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...props.queryParams,
      // 添加排序参数
      ...(sorter.value && sorter.value.columnKey
        ? {
            sortField: sorter.value.columnKey,
            sortOrder: sorter.value.order,
          }
        : {}),
      // 添加过滤参数
      ...filters.value,
    }

    const response = await fetchFn(params)

    data.value = response?.data?.list || []
    total.value = response?.data?.total || data.value.length
    updatePagination()
    emit('dataLoaded', { list: data.value, total: total.value })
  } catch (error) {
    emit('error', error)
    console.error('获取表格数据失败:', error)
  } finally {
    loading.value = false
    emit('update:loading', false)
  }
}

// 更新分页配置
const updatePagination = () => {
  paginationConfig.value = {
    ...paginationConfig.value,
    total: total.value,
    current: currentPage.value,
    pageSize: pageSize.value,
  }
}

// 监听数据源变化
watch(
  () => props.dataSource,
  () => {
    if (!isApiMode.value && Array.isArray(props.dataSource)) {
      handleListData()
    }
  },
  { deep: true },
)

// 监听查询参数变化
watch(
  () => props.queryParams,
  () => {
    if (isApiMode.value) {
      currentPage.value = 1 // 重置到第一页
      fetchTableData()
    }
  },
  { deep: true },
)

// 对外暴露的方法
const refresh = () => {
  isApiMode.value ? fetchTableData() : handleListData()
}

defineExpose({
  refresh,
  tableData: data,
  loading,
  currentPage,
  pageSize,
  total,
})

// 初始化加载数据
onMounted(() => {
  if (props.autoLoad) {
    refresh()
  }
})
</script>

<style scoped>
.table-wrapper {
  width: 100%;
}

.table-wrapper :deep(.ant-table-pagination) {
  margin: 16px 0;
  justify-content: flex-end;
}
</style>
