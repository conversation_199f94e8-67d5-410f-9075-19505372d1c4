<template>
  <div v-if="hasPermission">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

interface Props {
  permission?: string
  permissions?: string[]
  mode?: 'all' | 'any' // all: 需要所有权限, any: 需要任一权限
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'any'
})

const userStore = useUserStore()

const hasPermission = computed(() => {
  if (props.permission) {
    return userStore.hasBtnPermission(props.permission)
  }

  if (props.permissions && props.permissions.length > 0) {
    if (props.mode === 'all') {
      return props.permissions.every(perm => userStore.hasBtnPermission(perm))
    } else {
      return props.permissions.some(perm => userStore.hasBtnPermission(perm))
    }
  }

  return true
})
</script>
