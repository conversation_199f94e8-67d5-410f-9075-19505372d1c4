后台管理系统微服务
# 功能模块
- 框架：
  - 自定义权限注解，默认的value为权限点名称,可以添加在方法上以生成权限点和类上以定义权限所属模块，生成的权限格式为：模块:权限点
- 登录认证：
  - 用户名密码+谷歌验证码登录（如果有谷歌验证码）
- 系统管理：
  - 用户管理，后台管理系统用户管理
    - 页面左侧展示部门树，用户可以选择部门，在右侧展示部门下用户列表
    - 用户列表包含：用户ID，用户名（必填项），昵称，邮箱，手机号，岗位，状态（开关），创建时间，操作（修改）
    - 列表上边有新增，导入，导出按钮。
    - 搜索条件：用户名（左右模糊匹配），邮箱（左右模糊匹配），手机号（左右模糊匹配），岗位，状态，创建时间（时间范围）
    - 新增用户：弹出新增用户页面，包含：用户名（必填项），昵称，邮箱，手机号，部门（必填项），岗位（必填项，多选），谷歌秘钥配置（可选），状态（开关，默认开启），密码（必填项），操作（保存，取消）
  - 部门管理：
    - 部门列表包含：部门ID，名称（必填项），排序（默认0），状态（开关，默认开启），创建时间，操作（新增：创建子部门，编辑：修改部门名称、排序、状态，删除：删除部门，删除前判断是否有用户）
    - 列表上方有新增，导入，导出按钮。
    - 新增部门：弹出新增部门页面，包含：上级部门（可选，不选择则为顶级部门），部门名称（必填项），排序（默认0），状态（开关，默认开启），操作（保存，取消）
  - 岗位管理：
    - 列：岗位编码（必填项），名称（必填项），描述，排序（默认0），状态（开关，默认开启），创建时间，操作（编辑：修改岗位名称、描述、排序、状态，绑定角色， 删除：删除岗位，删除前判断是否有依赖的角色）
    - 列表上方有新增，导入，导出按钮。
    - 搜索条件：岗位编码（左右模糊匹配），名称（左右模糊匹配），状态
  - 角色管理：
    - 角色列表包含：角色ID，角色编码（必填项），角色名称（必填项），描述，状态（开关，默认开启），创建时间，操作（编辑：修改角色名称、描述、状态。菜单权限：分配菜单权限，接口权限：分配接口权限，删除：删除角色，自动解绑所有权限）
    - 列表上方有新增，导入，导出按钮。
    - 搜索条件：角色编码（左右模糊匹配），角色名称（左右模糊匹配），状态
    - 菜单权限：分配菜单权限，弹出菜单树，树节点选中即为拥有该菜单权限
    - 接口权限：分配接口权限，弹出接口树，树节点选中即为拥有该接口权限
  - 菜单管理：菜单的增删改查，菜单的配置


  
