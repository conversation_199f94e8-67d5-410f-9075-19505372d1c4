# 加密传输系统使用说明

## 概述

本系统实现了基于一次性RSA密钥对的加密传输机制，用于保护敏感数据在传输过程中的安全性，防止中间人攻击。

## 系统架构

### 核心组件

1. **KeyPairService** - 密钥对管理服务
2. **EncryptionInterceptor** - 请求拦截器
3. **EncryptionController** - 公钥获取接口
4. **RSAUtil** - RSA加密工具类
5. **@RequireEncryption** - 加密注解

### 工作流程

1. 前端调用 `/security/encryption/public-key` 获取公钥
2. 前端使用公钥加密请求数据
3. 前端在请求头中携带公钥和加密数据
4. 后端拦截器验证公钥并解密数据
5. 如果公钥无效，返回新公钥供前端重新加密

## 配置说明

### 后端配置 (application.yaml)

```yaml
venus:
  security:
    encryption:
      enabled: true                    # 是否启用加密
      key-size: 2048                  # RSA密钥长度
      key-expire-time: 300            # 密钥过期时间（秒）
      key-prefix: "encryption:keypair:" # Redis密钥前缀
      algorithm: "RSA"                # 加密算法
      transformation: "RSA/ECB/PKCS1Padding" # 加密模式
```

### Redis配置

确保Redis配置正确，密钥对信息将存储在Redis中：

```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      password: your_password
```

## 使用方法

### 后端使用

#### 1. 标记需要加密的接口

```java
@RestController
@RequestMapping("/api/sensitive")
public class SensitiveController {
    
    // 必须加密的接口
    @PostMapping("/data")
    @RequireEncryption("敏感数据接口")
    public ResponseEntity<?> handleSensitiveData(@RequestBody SensitiveData data) {
        // 处理敏感数据
        return ResponseEntity.ok(result);
    }
    
    // 可选加密的接口
    @PostMapping("/optional")
    @RequireEncryption(value = "可选加密接口", required = false)
    public ResponseEntity<?> handleOptionalData(@RequestBody Data data) {
        // 处理数据
        return ResponseEntity.ok(result);
    }
}
```

#### 2. 类级别注解

```java
@RestController
@RequestMapping("/api/admin")
@RequireEncryption("管理员接口") // 整个Controller都需要加密
public class AdminController {
    // 所有方法都需要加密
}
```

### 前端使用

#### 1. 安装依赖

```bash
npm install jsencrypt
```

#### 2. 使用加密工具

```javascript
import { sendEncryptedRequest, initEncryption } from '@/utils/encryption'

// 初始化加密工具
await initEncryption()

// 发送加密请求
const data = { username: 'admin', password: '123456' }
const result = await sendEncryptedRequest('/api/admin/login', data)
```

#### 3. 手动加密

```javascript
import encryptionUtil from '@/utils/encryption'

// 获取公钥
await encryptionUtil.getPublicKey()

// 加密数据
const encryptedData = encryptionUtil.encrypt(data)

// 发送请求
const response = await fetch('/api/sensitive/data', {
  method: 'POST',
  headers: {
    'X-Public-Key': encryptionUtil.publicKey,
    'X-Encrypted-Data': encryptedData,
    'Content-Type': 'application/json'
  }
})
```

## API接口

### 获取公钥

**请求:**
```
GET /api/admin/security/encryption/public-key
```

**响应:**
```json
{
  "code": 0,
  "data": {
    "publicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...",
    "keyId": "abc123def456",
    "expireTime": 1640995200000,
    "algorithm": "RSA",
    "transformation": "RSA/ECB/PKCS1Padding"
  },
  "msg": "success"
}
```

### 清理过期密钥

**请求:**
```
GET /api/admin/security/encryption/clean-expired
```

## 错误码说明

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 4001 | 需要加密传输 | 获取新公钥并重新加密请求 |
| 4002 | 公钥无效或已过期 | 使用响应头中的新公钥重新加密 |
| 4003 | 解密失败 | 获取新公钥并重新加密请求 |
| 4004 | 密钥已过期 | 使用响应头中的新公钥重新加密 |
| 4005 | 加密数据格式无效 | 检查加密数据格式 |

## 安全特性

1. **一次性密钥对** - 每个密钥对都有过期时间，定期更换
2. **RSA加密** - 使用2048位RSA加密，安全性高
3. **自动重试** - 公钥过期时自动获取新公钥并重试
4. **灵活配置** - 支持必须加密和可选加密两种模式
5. **性能优化** - 密钥对缓存在Redis中，支持分布式部署

## 注意事项

1. **密钥长度限制** - RSA加密有数据长度限制，单次加密数据不能超过密钥长度-11字节
2. **性能考虑** - RSA加密性能较低，适合加密少量敏感数据
3. **Redis依赖** - 系统依赖Redis存储密钥对，确保Redis可用性
4. **时钟同步** - 确保服务器时钟同步，避免密钥过期时间判断错误

## 测试

### 后端测试

系统提供了测试接口：

- `/api/admin/test/secure/encrypted` - 必须加密的测试接口
- `/api/admin/test/secure/optional-encrypted` - 可选加密的测试接口  
- `/api/admin/test/secure/normal` - 普通测试接口

### 前端测试

访问 `/encryption-test` 页面进行功能测试。

## 故障排除

### 常见问题

1. **公钥获取失败**
   - 检查Redis连接
   - 检查加密配置是否正确

2. **解密失败**
   - 检查公钥是否匹配
   - 检查加密数据格式
   - 检查密钥是否过期

3. **性能问题**
   - 适当调整密钥过期时间
   - 考虑使用对称加密+非对称加密的混合方案

### 日志查看

系统会记录详细的加密相关日志，可以通过以下方式查看：

```bash
# 查看加密相关日志
grep "encryption" application.log
grep "KeyPairService" application.log
grep "EncryptionInterceptor" application.log
```
