<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.venus.exchange</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../../../parent/pom.xml</relativePath>
    </parent>

    <artifactId>admin-backend</artifactId>
    <packaging>jar</packaging>

    <properties>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.venus.exchange</groupId>
            <artifactId>venus-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.venus.exchange</groupId>
            <artifactId>venus-spring-boot3-starter-web</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.venus.exchange</groupId>
            <artifactId>venus-spring-boot3-starter-mybatis</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.venus.exchange</groupId>
            <artifactId>venus-spring-boot3-starter-redis</artifactId>
            <version>${project.version}</version>
        </dependency>


        <!-- Spring Boot Actuator -->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-actuator</artifactId>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>de.codecentric</groupId>-->
        <!--            <artifactId>spring-boot-admin-starter-client</artifactId>-->
        <!--            <version>3.2.0</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.apache.dubbo</groupId>-->
        <!--            <artifactId>dubbo-spring-boot-starter</artifactId>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.kafka</groupId>-->
        <!--            <artifactId>spring-kafka</artifactId>-->
        <!--        </dependency>-->

    </dependencies>

    <build>
        <plugins>
            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <!-- Native Image Plugin -->
            <plugin>
                <groupId>org.graalvm.buildtools</groupId>
                <artifactId>native-maven-plugin</artifactId>
            </plugin>

            <!-- Docker Maven Plugin -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- Native Image Profile -->
        <profile>
            <id>native</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>build-native</id>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                                <phase>package</phase>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- Docker Profile -->
        <profile>
            <id>docker</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default</id>
                                <goals>
                                    <goal>build</goal>
                                    <goal>push</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project> 