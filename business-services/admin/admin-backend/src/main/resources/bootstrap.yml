dubbo:
  scan:
    base-packages: com.venus.exchange.admin.remote.service # Dubbo服务扫描基准包
  protocol:
    name: dubbo # Dubbo协议
    port: -1 # 端口号，-1表示随机
  registry:
    address: nacos://**************:8848 # 注册中心地址
  application:
    qos-enable: false # 关闭QOS
    service-discovery:
      proxy: instance # 使用基于实例的代理模式
  consumer:
    check: false # 关闭启动时检查
    timeout: 3000 # 全局超时时间
    retries: 0 # 重试次数
# config:
#   address: nacos://**************:8848