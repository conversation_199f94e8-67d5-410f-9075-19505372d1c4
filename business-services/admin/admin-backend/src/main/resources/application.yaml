server:
  port: 9080
  servlet:
    context-path: /api/admin # 应用上下文路径

spring:
  application:
    name: admin
  profiles:
    active: dev # 默认激活dev环境
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource # 指定使用Druid数据源

mybatis-plus:
  global-config:
    db-config:
      column-format: "`%s`" # 自动为所有字段名添加反引号
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

venus:
  company:
    name: Venus Exchange
  mybatis:
    mapper-package: com.venus.exchange.admin.**.mapper
  security:
    google-auth:
      enabled: true
    permission-scan:
      enable: true
    session:
      handler: redis  # redis | local
