spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,log4j2
      web-stat-filter:
        enabled: true
        url-pattern: /*
        excludes: '*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*'
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: 123456 # 请在生产环境中修改
        allow: # 允许访问的IP，为空则允许所有
        deny:
  

  # kafka:
  #   bootstrap-servers: **************:9092
  #   producer:
  #     retries: 3
  #     acks: all
  #     key-serializer: org.apache.kafka.common.serialization.StringSerializer
  #     value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
  #   consumer:
  #     group-id: venus-admin-group
  #     auto-offset-reset: earliest
  #     key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  #     value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
  #     properties:
  #       spring:
  #         json:
  #           trusted:
  #             packages: "*" # 信任所有包，以便进行JSON反序列化
  redis:
    redisson:
      config: |
        singleServerConfig:
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 3000
          retryAttempts: 4
          retryDelay: !<org.redisson.config.EqualJitterDelay> {baseDelay: PT1S, maxDelay: PT2S}
          reconnectionDelay: !<org.redisson.config.EqualJitterDelay> {baseDelay: PT0.1S, maxDelay: PT10S}
          password: 123456
          subscriptionsPerConnection: 5
          address: "redis://**************:6379"
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 50
          connectionMinimumIdleSize: 24
          connectionPoolSize: 64
          database: 0
          dnsMonitoringInterval: 5000
        threads: 16
        nettyThreads: 32
        codec: !<org.redisson.codec.Kryo5Codec> {}
        transportMode: "NIO"
        
        

logging:
  level:
    com.venus.exchange.admin: debug
    org.springframework: warn
    org.apache.dubbo: warn
    # MyBatis-Plus 核心日志
    com.baomidou.mybatisplus.core: warn
    # MyBatis-Plus 扩展日志
    com.baomidou.mybatisplus.extension: warn
    # MyBatis 核心日志
    org.mybatis.spring.mapper: warn
    # MyBatis 扫描日志
    org.mybatis.spring.boot.autoconfigure: warn