package com.venus.exchange.admin.system.vo;

import com.venus.exchange.admin.system.entity.SysDepartment;
import com.venus.framework.common.enums.SwitchStatusEnum;
import com.venus.framework.common.mapstruct.EntityMapping;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@EntityMapping(entity = SysDepartment.class)
public class DepartmentVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4116541244928142472L;
    /**
     * 部门ID
     */
    private Long id;
    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 部门名称
     */
    @NotBlank(groups = {Create.class, Update.class})
    private String name;
    /**
     * 显示顺序
     */
    private Integer sort;
    /**
     * 部门状态（0正常 1停用）
     */
    private SwitchStatusEnum status;
    /**
     * 逻辑删除
     */
    private Boolean deleted;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 子部门列表
     */
    private List<DepartmentVO> children;

    public interface Create {
    }

    public interface Update {
    }
}
