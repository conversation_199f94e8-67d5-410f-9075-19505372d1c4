package com.venus.exchange.admin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.venus.exchange.admin.system.entity.SysUserRole;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 用户角色服务接口
 */
@Validated
public interface ISysUserRoleService extends IService<SysUserRole> {

    /**
     * 根据用户ID获取角色ID列表
     */
    List<Long> getRoleIdsByUserId(@NotNull Long userId);
}
