package com.venus.exchange.admin.system.vo;

import com.venus.exchange.admin.system.entity.SysRole;
import com.venus.framework.common.enums.SwitchStatusEnum;
import com.venus.framework.common.mapstruct.EntityMapping;
import com.venus.framework.mybatis.pagination.PaginationParam;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 角色请求VO
 * </p>
 */
@Getter
@Setter
@EntityMapping(entity = SysRole.class)
public class RoleRequestVO extends PaginationParam implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空", groups = {Create.class, Update.class})
    private String code;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空", groups = {Create.class, Update.class})
    private String name;

    /**
     * 角色描述
     */
    private String desc;

    /**
     * 角色状态（0正常 1停用）
     */
    private SwitchStatusEnum status;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public interface Create {
    }

    public interface Update {
    }
}
