package com.venus.exchange.admin.system.vo;

import com.venus.exchange.admin.system.entity.SysMenu;
import com.venus.framework.common.enums.MenuTypeEnum;
import com.venus.framework.common.enums.SwitchStatusEnum;
import com.venus.framework.common.mapstruct.EntityMapping;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@EntityMapping(entity = SysMenu.class)
public class MenuVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4116541244928142472L;

    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 菜单名称
     */
    @NotBlank(groups = {Create.class, Update.class})
    private String name;

    /**
     * 权限标识
     */
    private String permission;

    /**
     * 菜单类型（1目录 2菜单 3按钮）
     */
    @NotNull(groups = {Create.class, Update.class})
    private MenuTypeEnum type;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 菜单状态（true正常 false停用）
     */
    private SwitchStatusEnum status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 子菜单列表
     */
    private List<MenuVO> children;

    public interface Create {
    }

    public interface Update {
    }
}
