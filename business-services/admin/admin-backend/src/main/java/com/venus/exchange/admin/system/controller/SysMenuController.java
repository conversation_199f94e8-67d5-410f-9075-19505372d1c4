package com.venus.exchange.admin.system.controller;

import com.venus.exchange.admin.system.service.ISysMenuService;
import com.venus.exchange.admin.system.vo.MenuVO;
import com.venus.framework.common.security.annotation.Permission;
import com.venus.framework.mybatis.pagination.PaginationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 菜单管理
 * </p>
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/sys/menu")
@RequiredArgsConstructor
@Permission(code = "sys:menu", desc = "菜单管理")
public class SysMenuController {
    private final ISysMenuService menuService;

    /**
     * 获取当前用户可访问的菜单树形结构
     */
    @GetMapping("/tree")
    @Permission(code = "sys:menu:query", desc = "查询菜单")
    public PaginationResult<MenuVO> getMenuTree() {
        List<MenuVO> list = menuService.getMenuTreeByRoleIds(null);
        return PaginationResult.of(0L, list);
    }

    /**
     * 新增菜单
     */
    @PostMapping
    @Permission(code = "sys:menu:create", desc = "新增菜单")
    public boolean addMenu(@Validated(MenuVO.Create.class) @RequestBody MenuVO vo) {
        return menuService.create(vo);
    }

    /**
     * 修改菜单
     */
    @PutMapping
    @Permission(code = "sys:menu:modify", desc = "修改菜单")
    public boolean updateMenu(@Validated(MenuVO.Update.class) @RequestBody MenuVO vo) {
        return menuService.update(vo);
    }

    /**
     * 删除菜单
     */
    @DeleteMapping("/{id}")
    @Permission(code = "sys:menu:del", desc = "删除菜单")
    public boolean deleteMenu(@PathVariable("id") Long id) {
        return menuService.removeById(id);
    }


}
