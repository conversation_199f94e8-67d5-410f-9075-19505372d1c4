package com.venus.exchange.admin.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.venus.exchange.admin.system.entity.SysMenu;
import com.venus.exchange.admin.system.mapper.SysMenuMapper;
import com.venus.exchange.admin.system.service.ISysMenuService;
import com.venus.exchange.admin.system.service.ISysRoleMenuService;
import com.venus.exchange.admin.system.vo.MenuVO;
import com.venus.exchange.admin.system.vo.MenuVOMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 菜单权限表 服务实现类
 * </p>
 *
 * @since 2025-06-22
 */
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements ISysMenuService {
    private final ISysRoleMenuService roleMenuService;

    @Override
    public boolean create(MenuVO vo) {
        SysMenu menu = MenuVOMapper.INSTANCE.toEntity(vo);
        return save(menu);
    }

    @Override
    public boolean update(MenuVO vo) {
        SysMenu menu = MenuVOMapper.INSTANCE.toEntity(vo);
        return updateById(menu);
    }

    @Override
    public List<MenuVO> getMenuTreeByRoleIds(List<Long> roleIds) {
        List<MenuVO> menus = getMenuByRoleIds(roleIds);
        if (CollectionUtils.isEmpty(menus)) {
            return new ArrayList<>();
        }
        return this.buildTree(menus);
    }

    @Override
    public List<MenuVO> getMenuByRoleIds(List<Long> roleIds) {
        // 如果角色ID列表为空，则返回所有菜单
        if (CollectionUtils.isEmpty(roleIds)) {
            return list(new LambdaQueryWrapper<SysMenu>()
                    .orderByAsc(SysMenu::getSort)).stream().map(MenuVOMapper.INSTANCE::toVO).toList();
        }

        List<Long> menuIds = roleMenuService.getMenuIdsByRoleIds(roleIds);
        if (CollectionUtils.isEmpty(menuIds)) {
            return new ArrayList<>();
        }

        return list(new LambdaQueryWrapper<SysMenu>()
                .in(SysMenu::getId, menuIds)
                .orderByAsc(SysMenu::getSort))
                .stream().map(MenuVOMapper.INSTANCE::toVO).toList();
    }

    /**
     * 构建树形结构
     */
    @Override
    public List<MenuVO> buildTree(List<MenuVO> menus) {
        // 按父ID分组
        Map<Long, List<MenuVO>> parentMap = menus.stream()
                .filter(menu -> menu.getParentId() != null)
                .collect(Collectors.groupingBy(MenuVO::getParentId));

        // 设置子菜单并按sort字段排序
        menus.forEach(menu -> {
            List<MenuVO> children = parentMap.get(menu.getId());
            if (children != null) {
                // 按sort字段升序排序
                children.sort(Comparator.comparing(MenuVO::getSort,
                        Comparator.nullsLast(Comparator.naturalOrder())));
                menu.setChildren(children);
            }
        });

        // 返回根节点（parentId为null或0的节点）并按sort排序
        return menus.stream()
                .filter(menu -> menu.getParentId() == null || menu.getParentId() == 0)
                .sorted(Comparator.comparing(MenuVO::getSort,
                        Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }

}
