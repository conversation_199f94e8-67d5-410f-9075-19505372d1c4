package com.venus.exchange.admin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.venus.exchange.admin.system.entity.SysRolePermission;

import java.util.List;

/**
 * 角色权限服务接口
 */
public interface ISysRolePermissionService extends IService<SysRolePermission> {

    /**
     * 根据角色ID列表获取权限列表
     */
    List<String> getPermissionsByRoleIds(List<Long> roleIds);
}
