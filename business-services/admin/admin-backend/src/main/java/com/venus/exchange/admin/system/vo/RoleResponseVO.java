package com.venus.exchange.admin.system.vo;

import com.venus.exchange.admin.system.entity.SysRole;
import com.venus.framework.common.enums.SwitchStatusEnum;
import com.venus.framework.common.mapstruct.EntityMapping;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 角色响应VO
 * </p>
 */
@Getter
@Setter
@EntityMapping(entity = SysRole.class)
public class RoleResponseVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色编码
     */
    private String code;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色描述
     */
    private String desc;

    /**
     * 角色状态（0正常 1停用）
     */
    private SwitchStatusEnum status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
