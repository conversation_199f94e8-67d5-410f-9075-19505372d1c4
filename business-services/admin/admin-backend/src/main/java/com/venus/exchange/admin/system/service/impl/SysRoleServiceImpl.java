package com.venus.exchange.admin.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.venus.exchange.admin.enums.ExceptionEnum;
import com.venus.exchange.admin.system.entity.SysRole;
import com.venus.exchange.admin.system.mapper.SysRoleMapper;
import com.venus.exchange.admin.system.service.ISysRoleService;
import com.venus.exchange.admin.system.vo.RoleRequestVO;
import com.venus.exchange.admin.system.vo.RoleRequestVOMapper;
import com.venus.exchange.admin.system.vo.RoleResponseVO;
import com.venus.exchange.admin.system.vo.RoleResponseVOMapper;
import com.venus.framework.common.exception.BusinessException;
import com.venus.framework.mybatis.pagination.PageUtil;
import com.venus.framework.mybatis.pagination.PaginationResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色信息表 服务实现类
 * </p>
 *
 * @since 2025-06-22
 */
@Service
@RequiredArgsConstructor
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements ISysRoleService {

    @Override
    public PaginationResult<RoleResponseVO> queryPage(RoleRequestVO vo) {
        LambdaQueryWrapper<SysRole> queryWrapper = Wrappers.lambdaQuery();

        if (vo.getId() != null) {
            queryWrapper.eq(SysRole::getId, vo.getId());
        }
        if (StringUtils.hasText(vo.getCode())) {
            queryWrapper.like(SysRole::getCode, vo.getCode());
        }
        if (StringUtils.hasText(vo.getName())) {
            queryWrapper.like(SysRole::getName, vo.getName());
        }
        if (vo.getStatus() != null) {
            queryWrapper.eq(SysRole::getStatus, vo.getStatus());
        }

        queryWrapper.eq(SysRole::getDeleted, false);
        queryWrapper.orderByDesc(SysRole::getCreateTime);

        Page<SysRole> page = page(PageUtil.pageOf(vo), queryWrapper);
        List<RoleResponseVO> list = page.getRecords().stream()
                .map(RoleResponseVOMapper.INSTANCE::toVO)
                .collect(Collectors.toList());

        return PaginationResult.of(page.getTotal(), list);
    }

    @Override
    public boolean create(RoleRequestVO vo) {
        // 检查角色编码是否已存在
        SysRole existingRole = getOne(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getCode, vo.getCode())
                .eq(SysRole::getDeleted, false));

        if (existingRole != null) {
            throw new BusinessException(ExceptionEnum.ROLE_CODE_EXISTS);
        }

        SysRole role = RoleRequestVOMapper.INSTANCE.toEntity(vo);
        return save(role);
    }

    @Override
    public boolean update(RoleRequestVO vo) {
        SysRole existingRole = getById(vo.getId());
        if (existingRole == null || existingRole.getDeleted()) {
            throw new BusinessException(ExceptionEnum.ROLE_NOT_EXISTS);
        }

        // 检查角色编码是否已被其他角色使用
        SysRole codeExistingRole = getOne(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getCode, vo.getCode())
                .ne(SysRole::getId, vo.getId())
                .eq(SysRole::getDeleted, false));

        if (codeExistingRole != null) {
            throw new BusinessException(ExceptionEnum.ROLE_CODE_EXISTS);
        }

        SysRole role = RoleRequestVOMapper.INSTANCE.toEntity(vo);
        return updateById(role);
    }
}
