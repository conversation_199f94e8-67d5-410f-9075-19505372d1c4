package com.venus.exchange.admin.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.venus.exchange.admin.system.entity.SysDepartment;
import com.venus.exchange.admin.system.mapper.SysDepartmentMapper;
import com.venus.exchange.admin.system.service.ISysDepartmentService;
import com.venus.exchange.admin.system.vo.DepartmentVO;
import com.venus.exchange.admin.system.vo.DepartmentVOMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * @since 2025-06-22
 */
@Service
public class SysDepartmentServiceImpl extends ServiceImpl<SysDepartmentMapper, SysDepartment> implements ISysDepartmentService {

    @Override
    public List<DepartmentVO> getDepartmentTree() {
        // 查询所有未删除的部门
        List<SysDepartment> allDepartments = list(new LambdaQueryWrapper<SysDepartment>()
                .orderByAsc(SysDepartment::getSort));
        if (CollectionUtils.isEmpty(allDepartments)) {
            return null;
        }

        // 转换为VO
        List<DepartmentVO> departmentVOs = allDepartments.stream()
                .map(DepartmentVOMapper.INSTANCE::toVO)
                .collect(Collectors.toList());

        // 构建树形结构
        return buildTree(departmentVOs);
    }

    @Override
    public List<Long> getDepartmentAndChildrenIds(Long deptId) {
        if (deptId == null || deptId <= 0) {
            return new ArrayList<>();
        }

        List<Long> result = new ArrayList<>();
        result.add(deptId); // 添加当前部门ID

        // 查询所有未删除的部门
        List<SysDepartment> allDepartments = list(new LambdaQueryWrapper<SysDepartment>()
                .eq(SysDepartment::getDeleted, false));

        if (!CollectionUtils.isEmpty(allDepartments)) {
            // 递归查找所有子部门ID
            findChildrenIds(deptId, allDepartments, result);
        }

        return result;
    }

    /**
     * 递归查找子部门ID
     */
    private void findChildrenIds(Long parentId, List<SysDepartment> allDepartments, List<Long> result) {
        List<SysDepartment> children = allDepartments.stream()
                .filter(dept -> parentId.equals(dept.getParentId()))
                .toList();

        for (SysDepartment child : children) {
            result.add(child.getId());
            // 递归查找子部门的子部门
            findChildrenIds(child.getId(), allDepartments, result);
        }
    }

    /**
     * 构建树形结构
     */
    private List<DepartmentVO> buildTree(List<DepartmentVO> departments) {
        // 按父ID分组
        Map<Long, List<DepartmentVO>> parentMap = departments.stream()
                .filter(dept -> dept.getParentId() != null)
                .collect(Collectors.groupingBy(DepartmentVO::getParentId));

        // 设置子部门并按sort字段排序
        departments.forEach(dept -> {
            List<DepartmentVO> children = parentMap.get(dept.getId());
            if (children != null) {
                // 按sort字段升序排序
                children.sort(Comparator.comparing(DepartmentVO::getSort,
                        Comparator.nullsLast(Comparator.naturalOrder())));
                dept.setChildren(children);
            }
        });

        // 返回根节点（parentId为null或0的节点）并按sort排序
        return departments.stream()
                .filter(dept -> dept.getParentId() == null || dept.getParentId() == 0)
                .sorted(Comparator.comparing(DepartmentVO::getSort,
                        Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }
}
