package com.venus.exchange.admin.system.controller;

import com.venus.exchange.admin.system.service.ISysPermissionService;
import com.venus.framework.common.security.annotation.Permission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 权限管理
 * </p>
 */
@Slf4j
@Permission(code = "sys:permission", desc = "权限管理")
@Validated
@RestController
@RequestMapping("/sys/permission")
@RequiredArgsConstructor
public class SysPermissionController {
    private final ISysPermissionService permissionService;

    @GetMapping("/scan")
    @Permission(code = "sys:permission:scan", desc = "扫描权限")
    public void scanPermission() {
        permissionService.scanPermission();
    }
}
