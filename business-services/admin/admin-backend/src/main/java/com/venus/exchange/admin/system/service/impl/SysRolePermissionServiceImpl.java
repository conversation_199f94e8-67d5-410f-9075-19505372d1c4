package com.venus.exchange.admin.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.venus.exchange.admin.system.entity.SysRolePermission;
import com.venus.exchange.admin.system.mapper.SysRolePermissionMapper;
import com.venus.exchange.admin.system.service.ISysRolePermissionService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 角色权限表 服务实现类
 * </p>
 *
 * @since 2025-06-22
 */
@Service
public class SysRolePermissionServiceImpl extends ServiceImpl<SysRolePermissionMapper, SysRolePermission> implements ISysRolePermissionService {

    @Override
    public List<String> getPermissionsByRoleIds(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) return List.of();

        return list(new LambdaQueryWrapper<SysRolePermission>().in(SysRolePermission::getRoleId, roleIds))
                .stream()
                .map(SysRolePermission::getPermissionCode).toList();
    }
}
