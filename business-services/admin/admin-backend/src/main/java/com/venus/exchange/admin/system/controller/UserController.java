package com.venus.exchange.admin.system.controller;

import com.venus.exchange.admin.system.service.ISysUserService;
import com.venus.exchange.admin.system.vo.UserRequestVO;
import com.venus.exchange.admin.system.vo.UserResponseVO;
import com.venus.framework.common.security.annotation.Permission;
import com.venus.framework.mybatis.pagination.PaginationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用户管理
 * </p>
 *
 * @since 2025-06-22
 */
@Slf4j
@Permission(code = "sys:user", desc = "用户管理")
@Validated
@RestController
@RequestMapping("/sys/user")
@RequiredArgsConstructor
public class UserController {
    private final ISysUserService userService;

    /**
     * 分页
     */
    @GetMapping
    @Permission(code = "sys:user:query", desc = "查询用户")
    public PaginationResult<UserResponseVO> queryPage(UserRequestVO vo) {
        return userService.queryPage(vo);
    }

    /**
     * 新增用户
     */
    @PostMapping
    @Permission(code = "sys:user:create", desc = "新增用户")
    public boolean createUser(@Validated(UserRequestVO.Create.class) @RequestBody UserRequestVO vo) {
        return userService.create(vo);
    }

    /**
     * 编辑用户
     */
    @PutMapping
    @Permission(code = "sys:user:modify", desc = "修改用户")
    public boolean modifyUser(@Validated(UserRequestVO.Update.class) @RequestBody UserRequestVO vo) {
        return userService.update(vo);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @Permission(code = "sys:user:del", desc = "删除用户")
    public boolean deleteUser(@PathVariable("id") Long id) {
        return userService.removeById(id);
    }
}
