package com.venus.exchange.admin.system.controller;

import com.venus.framework.common.security.annotation.Permission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 国际化管理
 * </p>
 */
@Slf4j
@Permission(code = "sys:i18n", desc = "国际化管理")
@Validated
@RestController
@RequestMapping("/sys/i18n")
@RequiredArgsConstructor
public class SysI18nController {

}
