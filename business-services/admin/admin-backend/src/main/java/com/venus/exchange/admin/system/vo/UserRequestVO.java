package com.venus.exchange.admin.system.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.venus.exchange.admin.system.entity.SysUser;
import com.venus.framework.common.enums.UserStatusEnum;
import com.venus.framework.common.mapstruct.EntityMapping;
import com.venus.framework.mybatis.pagination.PaginationParam;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

@Getter
@Setter
@EntityMapping(entity = SysUser.class)
public class UserRequestVO extends PaginationParam {
    @Serial
    private static final long serialVersionUID = 3632232886805971008L;
    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户名
     */
    @NotBlank(message = "'username' is required", groups = {UserRequestVO.Create.class, UserRequestVO.Update.class})
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 岗位编号数组
     */
    private String postIds;
    private UserStatusEnum status;

    public interface Create {
    }

    public interface Update {
    }

}
