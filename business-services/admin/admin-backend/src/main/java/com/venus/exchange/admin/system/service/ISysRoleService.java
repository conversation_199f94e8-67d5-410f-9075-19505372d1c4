package com.venus.exchange.admin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.venus.exchange.admin.system.entity.SysRole;
import com.venus.exchange.admin.system.vo.RoleRequestVO;
import com.venus.exchange.admin.system.vo.RoleResponseVO;
import com.venus.framework.mybatis.pagination.PaginationResult;

/**
 * <p>
 * 角色信息表 服务类
 * </p>
 *
 * @since 2025-06-22
 */
public interface ISysRoleService extends IService<SysRole> {

    /**
     * 分页查询角色
     */
    PaginationResult<RoleResponseVO> queryPage(RoleRequestVO vo);

    /**
     * 创建角色
     */
    boolean create(RoleRequestVO vo);

    /**
     * 更新角色
     */
    boolean update(RoleRequestVO vo);
}
