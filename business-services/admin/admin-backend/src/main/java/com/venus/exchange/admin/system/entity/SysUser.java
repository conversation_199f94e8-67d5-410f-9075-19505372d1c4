package com.venus.exchange.admin.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.venus.framework.common.enums.UserStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户信息表
 * </p>
 *
 * @since 2025-06-22
 */
@Getter
@Setter
@TableName("t_sys_user")
public class SysUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1464658215831899621L;
    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 部门ID
     */
    private Long deptId;

    @TableField(exist = false)
    private SysDepartment department;

    /**
     * 用户状态
     */
    private UserStatusEnum status;

    /**
     * 最后登录IP
     */
    private String loginIp;
    /**
     * 登录成功时间
     */
    private LocalDateTime loginTime;
    /**
     * 尝试登录时间，未登录成功也记录，用来判断尝试间隔
     */
    private LocalDateTime loginAttemptTime;
    /**
     * 登录错误尝试次数
     */
    private Integer loginAttemptTimes;
    /**
     * 密码
     */
    private String password;

    /**
     * 谷歌验证码密钥
     */
    private String googleSecret;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public boolean isAdmin() {
        return "admin".equals(this.username);
    }
}
