package com.venus.exchange.admin.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.venus.exchange.admin.system.entity.SysI18n;
import com.venus.exchange.admin.system.mapper.SysI18nMapper;
import com.venus.exchange.admin.system.service.ISysI18nService;
import com.venus.framework.web.context.RequestContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @since 2025-06-23
 */
@Service
@RequiredArgsConstructor
public class SysI18nServiceImpl extends ServiceImpl<SysI18nMapper, SysI18n> implements ISysI18nService {

    @Override
    public SysI18n getI18nMsg(String code) {
        LambdaQueryWrapper<SysI18n> wrapper = new LambdaQueryWrapper<>();
        return getOne(wrapper
                .eq(SysI18n::getAppName, RequestContext.getAppName())
                .eq(SysI18n::getLanguage, RequestContext.getLanguage())
                .eq(SysI18n::getCode, code)
        );
    }
}
