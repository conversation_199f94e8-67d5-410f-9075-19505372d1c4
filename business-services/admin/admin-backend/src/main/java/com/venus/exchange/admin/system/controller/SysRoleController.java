package com.venus.exchange.admin.system.controller;

import com.venus.exchange.admin.system.service.ISysRoleService;
import com.venus.exchange.admin.system.vo.RoleRequestVO;
import com.venus.exchange.admin.system.vo.RoleResponseVO;
import com.venus.framework.common.security.annotation.Permission;
import com.venus.framework.mybatis.pagination.PaginationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 角色管理
 * </p>
 */
@Slf4j
@Permission(code = "sys:role", desc = "角色管理")
@Validated
@RestController
@RequestMapping("/sys/role")
@RequiredArgsConstructor
public class SysRoleController {

    private final ISysRoleService roleService;

    /**
     * 分页查询角色
     */
    @GetMapping
    @Permission(code = "sys:role:query", desc = "查询角色")
    public PaginationResult<RoleResponseVO> queryPage(RoleRequestVO vo) {
        return roleService.queryPage(vo);
    }

    /**
     * 新增角色
     */
    @PostMapping
    @Permission(code = "sys:role:create", desc = "新增角色")
    public boolean createRole(@Validated(RoleRequestVO.Create.class) @RequestBody RoleRequestVO vo) {
        return roleService.create(vo);
    }

    /**
     * 修改角色
     */
    @PutMapping
    @Permission(code = "sys:role:modify", desc = "修改角色")
    public boolean updateRole(@Validated(RoleRequestVO.Update.class) @RequestBody RoleRequestVO vo) {
        return roleService.update(vo);
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @Permission(code = "sys:role:del", desc = "删除角色")
    public boolean deleteRole(@PathVariable("id") Long id) {
        return roleService.removeById(id);
    }
}
