package com.venus.exchange.admin.user.vo;

import com.venus.exchange.admin.system.vo.MenuVO;
import com.venus.exchange.admin.system.vo.UserResponseVO;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashSet;
import java.util.List;

@Data
public class LoginResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -1948640271613823196L;

    /**
     * 第一次验证返回的临时token,若无需二次验证则为空
     */
    private String preToken;
    /**
     * 用户信息
     */
    private UserResponseVO userInfo;

    /**
     * 用户菜单树
     */
    private List<MenuVO> menus;

    /**
     * 用户按钮权限
     */
    private HashSet<String> btnPerms;
}
