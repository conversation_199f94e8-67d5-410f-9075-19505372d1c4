package com.venus.exchange.admin.user.controller;

import com.venus.exchange.admin.enums.ExceptionEnum;
import com.venus.exchange.admin.system.entity.SysUser;
import com.venus.exchange.admin.system.service.ISysUserService;
import com.venus.exchange.admin.system.vo.UserRequestVO;
import com.venus.exchange.admin.system.vo.UserResponseVO;
import com.venus.exchange.admin.user.service.IUserProfileService;
import com.venus.framework.common.exception.BusinessException;
import com.venus.framework.common.security.annotation.NoAuth;
import com.venus.framework.common.util.TotpUtil;
import com.venus.framework.web.context.RequestContext;
import com.venus.framework.web.core.response.GlobalResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.InvalidKeyException;

/**
 * 认证
 */
@Slf4j
@RestController
@RequestMapping("/user/profile")
@RequiredArgsConstructor
public class UserProfileController {
    private final IUserProfileService userProfileService;
    private final ISysUserService userService;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/info")
    public UserResponseVO getCurrentUser() {
        return userProfileService.getCurrentUser();
    }

    /**
     * 更新当前用户信息
     */
    @PutMapping("/info")
    public boolean updateCurrentUser(@Validated @RequestBody UserRequestVO vo) {
        vo.setId(RequestContext.getUserId());
        return userService.update(vo);
    }

    /**
     * 生成谷歌秘钥，重复点击会覆盖
     */
    @GetMapping("/google/secret")
    public void generateGoogleSecret(HttpServletResponse response, @RequestParam("width") int width, @RequestParam("height") int height) {
        userProfileService.generateGoogleSecret(response, width, height);
    }

    /**
     * 获取测试用的谷歌验证码（开发环境）
     */
    @NoAuth
    @GetMapping("/gcode-test")
    public GlobalResponse<String> gCodeTest(@RequestParam("username") String username) throws InvalidKeyException {
        SysUser user = userService.getByName(username);
        if (user == null) {
            throw new BusinessException(ExceptionEnum.USER_NOT_EXISTS);
        }
        if (StringUtils.hasText(user.getGoogleSecret())) {
            String code = TotpUtil.generateCodeTest(user.getGoogleSecret());
            return GlobalResponse.success(code);
        }
        return GlobalResponse.success("");
    }
}