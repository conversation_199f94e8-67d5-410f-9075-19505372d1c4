package com.venus.exchange.admin.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * @since 2025-06-23
 */
@Getter
@Setter
@TableName("t_sys_i18n")
public class SysI18n implements Serializable {
    @Serial
    private static final long serialVersionUID = -3857160361062427776L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 时区
     */
    private String appName;
    /**
     * 时区
     */
    private String language;

    /**
     * 国际化key
     */
    private String code;

    /**
     * 国际化内容
     */
    private String msg;
}
