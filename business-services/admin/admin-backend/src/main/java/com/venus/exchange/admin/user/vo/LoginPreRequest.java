package com.venus.exchange.admin.user.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 登录请求DTO
 */
@Getter
@Setter
public class LoginPreRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = -7390559871470328155L;

    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "密码不能为空")
    private String password;
}
