package com.venus.exchange.admin.system.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.venus.exchange.admin.system.entity.SysDepartment;
import com.venus.exchange.admin.system.entity.SysUser;
import com.venus.framework.common.enums.UserStatusEnum;
import com.venus.framework.common.mapstruct.EntityMapping;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@EntityMapping(entity = SysUser.class)
public class UserResponseVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -274739804696246174L;
    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 部门ID
     */
    private Long deptId;
    private SysDepartment department;

    private UserStatusEnum status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * datetime
     */
    private LocalDateTime loginDate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
