package com.venus.exchange.admin.user.service;

import com.venus.exchange.admin.user.vo.LoginGoogleRequest;
import com.venus.exchange.admin.user.vo.LoginPreRequest;
import com.venus.exchange.admin.user.vo.LoginResponse;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;

@Validated
public interface IAuthService {
    LoginResponse tryLogin(@Validated LoginPreRequest loginRequest, HttpServletResponse response);

    LoginResponse loginWithGoogle(@Validated LoginGoogleRequest loginRequest, HttpServletResponse response);

    LoginResponse loginCheck(HttpServletResponse response);

    void logout();

}
