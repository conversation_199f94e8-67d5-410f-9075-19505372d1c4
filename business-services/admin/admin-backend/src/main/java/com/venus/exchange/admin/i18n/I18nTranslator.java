package com.venus.exchange.admin.i18n;

import com.venus.exchange.admin.system.entity.SysI18n;
import com.venus.exchange.admin.system.service.ISysI18nService;
import com.venus.framework.common.i18n.II18nTranslator;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Primary
@Component
@RequiredArgsConstructor
public class I18nTranslator implements II18nTranslator {
    private final ISysI18nService iSysI18nService;

    @Override
    public String translate(String code, String defaultMsg) {
        SysI18n i18n = iSysI18nService.getI18nMsg(code);
        return i18n == null ? (StringUtils.hasText(defaultMsg) ? defaultMsg : code) : i18n.getMsg();
    }
}
