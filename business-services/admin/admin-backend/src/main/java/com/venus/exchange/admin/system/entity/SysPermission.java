package com.venus.exchange.admin.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 权限表
 * </p>
 *
 * @since 2025-06-22
 */
@Getter
@Setter
@TableName("t_sys_permission")
public class SysPermission implements Serializable {

    @Serial
    private static final long serialVersionUID = -9043805895426724584L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 权限code
     */
    private String code;

    /**
     * 权限描述
     */
    private String desc;
    /**
     * 父级权限，一般是定义在controller上的权限
     */
    private String parent;
}
