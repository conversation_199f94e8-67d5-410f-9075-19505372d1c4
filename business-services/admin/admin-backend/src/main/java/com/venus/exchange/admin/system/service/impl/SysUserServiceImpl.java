package com.venus.exchange.admin.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.venus.exchange.admin.enums.ExceptionEnum;
import com.venus.exchange.admin.system.entity.SysDepartment;
import com.venus.exchange.admin.system.entity.SysUser;
import com.venus.exchange.admin.system.mapper.SysUserMapper;
import com.venus.exchange.admin.system.service.ISysDepartmentService;
import com.venus.exchange.admin.system.service.ISysUserService;
import com.venus.exchange.admin.system.vo.UserRequestVO;
import com.venus.exchange.admin.system.vo.UserRequestVOMapper;
import com.venus.exchange.admin.system.vo.UserResponseVO;
import com.venus.exchange.admin.system.vo.UserResponseVOMapper;
import com.venus.framework.common.exception.BusinessException;
import com.venus.framework.common.util.PasswordUtil;
import com.venus.framework.mybatis.pagination.PageUtil;
import com.venus.framework.mybatis.pagination.PaginationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户信息表 服务实现类
 * </p>
 *
 * @since 2025-06-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    private final SysUserMapper userMapper;
    private final ISysDepartmentService departmentService;

    @Override
    public PaginationResult<UserResponseVO> queryPage(UserRequestVO vo) {
        LambdaQueryWrapper<SysUser> queryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.hasText(vo.getUsername())) {
            queryWrapper.like(SysUser::getUsername, vo.getUsername().trim());
        }

        if (StringUtils.hasText(vo.getEmail())) {
            queryWrapper.like(SysUser::getEmail, vo.getEmail().trim());
        }

        if (StringUtils.hasText(vo.getPhone())) {
            queryWrapper.like(SysUser::getPhone, vo.getPhone().trim());
        }
        if (vo.getStatus() != null) {
            queryWrapper.eq(SysUser::getStatus, vo.getStatus());
        }
        if (vo.getDeptId() != null && vo.getDeptId() > 0) {
            List<Long> deptIds = departmentService.getDepartmentAndChildrenIds(vo.getDeptId());
            queryWrapper.in(SysUser::getDeptId, deptIds);
        }
        Page<SysUser> page = PageUtil.pageOf(vo);
        page(page, queryWrapper);


        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PaginationResult.of(page.getTotal(), null);
        }

        // 填充部门
        Set<Long> deptIds = page.getRecords().stream().map(SysUser::getDeptId).collect(Collectors.toSet());
        List<SysDepartment> departments = departmentService.listByIds(deptIds);
        if (!CollectionUtils.isEmpty(departments)) {
            Map<Long, SysDepartment> deptMap = departments.stream()
                    .collect(Collectors.toMap(
                            SysDepartment::getId,
                            department -> department
                    ));
            page.getRecords().forEach(row -> {
                if (row.getDeptId() != null) {
                    row.setDepartment(deptMap.get(row.getDeptId()));
                }
            });
        }

        // 转为VO，数据脱敏
        List<UserResponseVO> list = page.getRecords().stream().map(UserResponseVOMapper.INSTANCE::toVO).toList();
        return PaginationResult.of(page.getTotal(), list);
    }

    @Override
    public SysUser getByName(String username) {
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, username));
        if (user != null && user.getDeptId() != null) {
            user.setDepartment(departmentService.getById(user.getDeptId()));
        }
        return user;
    }

    @Override
    public boolean create(UserRequestVO vo) {
        SysUser user = UserRequestVOMapper.INSTANCE.toEntity(vo);
        user.setPassword(PasswordUtil.hashPassword(vo.getPassword()));

        return save(user);
    }

    @Override
    public boolean update(UserRequestVO vo) {
        SysUser old = getById(vo.getId());
        if (old == null) {
            throw new BusinessException(ExceptionEnum.USER_NOT_EXISTS);
        }

        SysUser user = UserRequestVOMapper.INSTANCE.toEntity(vo);
        if (vo.getPassword() != null) {
            user.setPassword(PasswordUtil.hashPassword(vo.getPassword()));
        }
        return updateById(user);
    }
}
