package com.venus.exchange.admin.user.service.impl;

import com.venus.exchange.admin.consts.Constants;
import com.venus.exchange.admin.consts.RedisKeys;
import com.venus.exchange.admin.enums.ExceptionEnum;
import com.venus.exchange.admin.system.entity.SysUser;
import com.venus.exchange.admin.system.service.*;
import com.venus.exchange.admin.system.vo.MenuVO;
import com.venus.exchange.admin.system.vo.UserResponseVOMapper;
import com.venus.exchange.admin.user.service.IAuthService;
import com.venus.exchange.admin.user.vo.LoginGoogleRequest;
import com.venus.exchange.admin.user.vo.LoginPreRequest;
import com.venus.exchange.admin.user.vo.LoginResponse;
import com.venus.framework.common.enums.MenuTypeEnum;
import com.venus.framework.common.enums.UserStatusEnum;
import com.venus.framework.common.exception.BusinessException;
import com.venus.framework.common.security.session.IAuthSessionHandler;
import com.venus.framework.common.security.session.Session;
import com.venus.framework.common.util.PasswordUtil;
import com.venus.framework.common.util.TotpUtil;
import com.venus.framework.redis.util.RedisClient;
import com.venus.framework.web.context.RequestContext;
import com.venus.framework.web.security.constants.WebSecurityConst;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 认证服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService implements IAuthService {
    private final RedisClient redis;
    private final ISysUserService userService;
    private final ISysUserRoleService userRoleService;
    private final ISysRolePermissionService rolePermissionService;
    private final ISysRoleMenuService roleMenuService;
    private final ISysMenuService menuService;
    private final IAuthSessionHandler sessionHandler;

    /**
     * 用户登录
     */
    public LoginResponse tryLogin(LoginPreRequest loginRequest, HttpServletResponse response) {
        SysUser user = userService.getByName(loginRequest.getUsername());
        if (user == null) {
            throw new BusinessException(ExceptionEnum.USER_NOT_EXISTS);
        }

        if (user.getStatus().equals(UserStatusEnum.BANNED)) {
            throw new BusinessException(ExceptionEnum.USER_BANNED);
        }

        LocalDateTime now = LocalDateTime.now();
        // 如果用户被冻结，且冻结时间未超过限制，则拒绝登录
        if (user.getStatus().equals(UserStatusEnum.FROZEN) &&
                user.getLoginAttemptTime().plusSeconds(Constants.USER_ATTEMPT_TIMES_EXPIRE).isAfter(now)) {
            throw new BusinessException(ExceptionEnum.USER_FROZEN);
        }

        user.setLoginAttemptTime(now);
        // 登录失败记录次数，次数超限则冻结场景
        if (!PasswordUtil.verifyPassword(loginRequest.getPassword(), user.getPassword())) {
            int attemptTimes = user.getLoginAttemptTimes() + 1;
            user.setLoginAttemptTimes(attemptTimes);
            if (attemptTimes >= Constants.USER_ATTEMPT_TIMES_MAX) {
                user.setStatus(UserStatusEnum.FROZEN);
            }
            userService.updateById(user);
            throw new BusinessException(ExceptionEnum.USER_WRONG_PASSWORD);
        }

        // 密码验证通过，处理二次验证
        // 需要二次验证google验证码，只返回临时token
        if (StringUtils.hasText(user.getGoogleSecret())) {
            String preToken = UUID.randomUUID().toString();
            redis.setString(String.format(RedisKeys.AUTH_PRE_TOKEN, preToken), user.getId(), 300, ChronoUnit.SECONDS);
            LoginResponse responseVO = new LoginResponse();
            responseVO.setPreToken(preToken);
            return responseVO;
        }
        return handleLoginSuccess(user, response, now);
    }

    public LoginResponse loginWithGoogle(LoginGoogleRequest loginRequest, HttpServletResponse response) {
        String tokenKey = String.format(RedisKeys.AUTH_PRE_TOKEN, loginRequest.getPreToken());
        Long userId = redis.<Long>getString(tokenKey);
        if (userId == null || userId == 0) {
            throw new BusinessException(ExceptionEnum.OPERATION_TIMEOUT);
        }

        SysUser user = userService.getById(userId);
        if (user == null) {
            throw new BusinessException(ExceptionEnum.USER_NOT_EXISTS);
        }

        if (!TotpUtil.verifyCode(user.getGoogleSecret(), loginRequest.getGoogleCode())) {
            throw new BusinessException(ExceptionEnum.USER_WRONG_GOOGLE_CODE);
        }
        redis.deleteString(tokenKey);
        return handleLoginSuccess(user, response, LocalDateTime.now());
    }

    private LoginResponse handleLoginSuccess(SysUser user, HttpServletResponse response, LocalDateTime now) {
        // 验证通过，重置用户状态
        user.setLoginAttemptTimes(0);
        user.setStatus(UserStatusEnum.NORMAL);
        user.setLoginTime(now);
        user.setLoginIp(RequestContext.getClientIp());
        userService.updateById(user);

        LoginResponse respVO = new LoginResponse();

        // 获取用户角色和权限
        HashSet<String> apiPerms = new HashSet<>();
        HashSet<String> btnPerms = new HashSet<>();
        if (user.isAdmin()) {
            apiPerms.add("*");
            btnPerms.add("*");
            respVO.setMenus(menuService.getMenuTreeByRoleIds(null));
        } else {
            // API permissions
            List<Long> roleIds = userRoleService.getRoleIdsByUserId(user.getId());
            if (!CollectionUtils.isEmpty(roleIds)) {
                apiPerms = new HashSet<>(rolePermissionService.getPermissionsByRoleIds(roleIds));
            }

            // 从菜单中提取按钮权限
            List<MenuVO> menus = menuService.getMenuByRoleIds(roleIds);
            if (!CollectionUtils.isEmpty(menus)) {
                respVO.setMenus(menuService.buildTree(menus));
                btnPerms = (HashSet<String>) menus.stream()
                        .filter(menuVO -> menuVO.getType().equals(MenuTypeEnum.BUTTON))
                        .map(MenuVO::getPermission)
                        .collect(Collectors.toSet());
            }

        }

        respVO.setUserInfo(UserResponseVOMapper.INSTANCE.toVO(user));
        respVO.setBtnPerms(btnPerms);


        // 保存session到缓存
        Session session = Session.create();
        session.setUserId(user.getId());
        session.setPermissions(apiPerms);
        sessionHandler.saveSession(RequestContext.getAppName(), RequestContext.getDeviceType(), session);

        // 将sessionId放入响应头
        response.setHeader(WebSecurityConst.HEADER_SESSION_ID, session.getId());
        return respVO;
    }

    @Override
    public LoginResponse loginCheck(HttpServletResponse response) {
        SysUser user = userService.getById(RequestContext.getUserId());
        if (user == null) {
            throw new BusinessException(ExceptionEnum.USER_NOT_EXISTS);
        }

        return handleLoginSuccess(user, response, LocalDateTime.now());
    }

    /**
     * 用户登出
     */
    @Override
    public void logout() {
        sessionHandler.removeSession(RequestContext.getAppName(), RequestContext.getDeviceType(), RequestContext.getSessionId());
    }


}
