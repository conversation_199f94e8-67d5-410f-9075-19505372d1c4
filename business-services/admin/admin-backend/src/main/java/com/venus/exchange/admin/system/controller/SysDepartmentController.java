package com.venus.exchange.admin.system.controller;

import com.venus.exchange.admin.system.service.ISysDepartmentService;
import com.venus.exchange.admin.system.vo.DepartmentVO;
import com.venus.exchange.admin.system.vo.DepartmentVOMapper;
import com.venus.framework.common.security.annotation.Permission;
import com.venus.framework.mybatis.pagination.PaginationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 部门管理
 * </p>
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/sys/dept")
@RequiredArgsConstructor
@Permission(code = "sys:dept", desc = "部门管理")
public class SysDepartmentController {
    private final ISysDepartmentService departmentService;

    /**
     * 获取部门树形结构
     */
    @GetMapping("/tree")
    @Permission(code = "sys:dept:query", desc = "查询部门")
    public PaginationResult<DepartmentVO> getDepartmentTree() {
        List<DepartmentVO> list = departmentService.getDepartmentTree();
        return PaginationResult.of(0L, list);
    }

    /**
     * 新增部门
     */
    @PostMapping
    @Permission(code = "sys:dept:create", desc = "新增部门")
    public boolean addDept(@Validated(DepartmentVO.Create.class) @RequestBody DepartmentVO vo) {
        return departmentService.save(DepartmentVOMapper.INSTANCE.toEntity(vo));
    }

    /**
     * 修改部门
     */
    @PutMapping
    @Permission(code = "sys:dept:modify", desc = "修改部门")
    public boolean updateDept(@Validated(DepartmentVO.Update.class) @RequestBody DepartmentVO vo) {
        return departmentService.updateById(DepartmentVOMapper.INSTANCE.toEntity(vo));
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/{id}")
    @Permission(code = "sys:dept:del", desc = "删除部门")
    public boolean deleteDept(@PathVariable("id") Long id) {
        return departmentService.removeById(id);
    }


}
