package com.venus.exchange.admin.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.venus.exchange.admin.consts.RedisKeys;
import com.venus.exchange.admin.enums.ExceptionEnum;
import com.venus.exchange.admin.system.entity.SysPermission;
import com.venus.exchange.admin.system.mapper.SysPermissionMapper;
import com.venus.exchange.admin.system.service.ISysPermissionService;
import com.venus.framework.common.exception.BusinessException;
import com.venus.framework.common.permission.CommonPermission;
import com.venus.framework.common.util.StringUtil;
import com.venus.framework.redis.util.RedisClient;
import com.venus.framework.web.security.permission.PermissionScanner;
import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 权限表 服务实现类
 * </p>
 *
 * @since 2025-06-22
 */
@Service
@RequiredArgsConstructor
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission> implements ISysPermissionService {
    private final RedisClient redis;
    private final PermissionScanner permissionScanner;
    private final Environment environment;

    @Override
    @Transactional
    public void scanPermission() {
        if (!redis.tryLock(RedisKeys.PERMISSION_SCAN_LOCK)) {
            throw new BusinessException(ExceptionEnum.PERMISSION_SCANNING);
        }

        try {
            HashMap<String, CommonPermission> permissionsFromCode = permissionScanner.scanPermissionsFromCode();

            Set<SysPermission> toAdd = new HashSet<>();
            Set<SysPermission> toDelete = new HashSet<>();
            Set<SysPermission> toUpdate = new HashSet<>();

            // 将 oldPermissions 按 code 建立 map，便于查找
            Map<String, SysPermission> oldPermMap = list(new LambdaQueryWrapper<>()).stream()
                    .collect(Collectors.toMap(SysPermission::getCode, p -> p));

            // 扫描新权限，判断是 add 还是 update
            for (CommonPermission newPerm : permissionsFromCode.values()) {
                if (oldPermMap.containsKey(newPerm.getCode())) {
                    // 存在，检查是否需要更新（desc 或 parent 不同）
                    SysPermission oldPerm = oldPermMap.get(newPerm.getCode());
                    if (!StringUtil.equalsNormalize(oldPerm.getDesc(), newPerm.getDesc()) ||
                            !StringUtil.equalsNormalize(oldPerm.getParent(), newPerm.getParent())) {
                        // 描述或父级不同，标记为更新
                        oldPerm.setDesc(newPerm.getDesc());
                        oldPerm.setParent(newPerm.getParent());
                        toUpdate.add(oldPerm);
                    }
                } else {
                    // 不存在，标记为新增
                    SysPermission newPermEntity = new SysPermission();
                    newPermEntity.setCode(newPerm.getCode());
                    newPermEntity.setDesc(newPerm.getDesc());
                    newPermEntity.setParent(newPerm.getParent());
                    toAdd.add(newPermEntity);
                }
            }

            // 找出数据库中存在但代码中没有的权限，标记为删除
            for (SysPermission oldPerm : oldPermMap.values()) {
                if (permissionsFromCode.keySet().stream().noneMatch(oldKey -> oldKey.equals(oldPerm.getCode()))) {
                    toDelete.add(oldPerm);
                }
            }

            // 执行增删改操作
            if (!toAdd.isEmpty()) {
                saveBatch(toAdd);
            }
            if (!toDelete.isEmpty()) {
                removeByIds(toDelete.stream().map(SysPermission::getId).toList());
            }
            if (!toUpdate.isEmpty()) {
                updateBatchById(toUpdate);
            }
        } finally {
            redis.unlock(RedisKeys.PERMISSION_SCAN_LOCK);
        }
    }
}
