package com.venus.exchange.admin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.venus.exchange.admin.system.entity.SysDepartment;
import com.venus.exchange.admin.system.vo.DepartmentVO;

import java.util.List;

/**
 * <p>
 * 部门表 服务类
 * </p>
 *
 * @since 2025-06-22
 */
public interface ISysDepartmentService extends IService<SysDepartment> {

    /**
     * 获取部门树形结构
     */
    List<DepartmentVO> getDepartmentTree();

    /**
     * 获取指定部门及其所有子部门的ID列表
     */
    List<Long> getDepartmentAndChildrenIds(Long deptId);
}
