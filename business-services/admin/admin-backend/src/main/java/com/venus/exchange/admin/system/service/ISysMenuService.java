package com.venus.exchange.admin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.venus.exchange.admin.system.entity.SysMenu;
import com.venus.exchange.admin.system.vo.MenuVO;

import java.util.List;

/**
 * <p>
 * 菜单权限表 服务类
 * </p>
 *
 * @since 2025-06-22
 */
public interface ISysMenuService extends IService<SysMenu> {
    /**
     * 创建菜单
     */
    boolean create(MenuVO vo);

    /**
     * 更新菜单
     */
    boolean update(MenuVO vo);


    /**
     * 获取用户可访问的菜单树形结构
     */
    List<MenuVO> getMenuTreeByRoleIds(List<Long> roleIds);

    /**
     * 获取用户可访问的菜单列表
     */
    List<MenuVO> getMenuByRoleIds(List<Long> roleIds);

    List<MenuVO> buildTree(List<MenuVO> menus);
}
