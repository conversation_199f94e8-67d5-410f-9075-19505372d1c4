package com.venus.exchange.admin.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.venus.exchange.admin.system.entity.SysUser;
import com.venus.exchange.admin.system.vo.UserRequestVO;
import com.venus.exchange.admin.system.vo.UserResponseVO;
import com.venus.framework.mybatis.pagination.PaginationResult;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

/**
 * <p>
 * 用户信息表 服务类
 * </p>
 *
 * @since 2025-06-22
 */
@Validated
public interface ISysUserService extends IService<SysUser> {

    PaginationResult<UserResponseVO> queryPage(UserRequestVO vo);

    SysUser getByName(@NotBlank String username);

    boolean create(UserRequestVO vo);

    boolean update(UserRequestVO vo);
}
