package com.venus.exchange.admin.user.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
public class LoginGoogleRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = -5343813645996800975L;

    /**
     * 临时token
     */
    @NotBlank(message = "Token can't be blank")
    private String preToken;
    /**
     * 谷歌验证码
     */
    @NotBlank(message = "Google code can't be blank")
    private String googleCode;
}
