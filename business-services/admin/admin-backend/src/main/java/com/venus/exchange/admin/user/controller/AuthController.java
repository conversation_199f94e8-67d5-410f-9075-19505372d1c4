package com.venus.exchange.admin.user.controller;

import com.venus.exchange.admin.user.service.IAuthService;
import com.venus.exchange.admin.user.vo.LoginGoogleRequest;
import com.venus.exchange.admin.user.vo.LoginPreRequest;
import com.venus.exchange.admin.user.vo.LoginResponse;
import com.venus.framework.common.security.annotation.NoAuth;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 认证授权
 */
@Slf4j
@RestController
@RequestMapping("/user/auth")
@RequiredArgsConstructor
public class AuthController {

    private final IAuthService authService;

    /**
     * 用户预登录,第一步：验证用户名密码，通过后返回临时token，用于第二步验证
     * 使用加密传输保护登录凭据
     */
    @NoAuth
    @PostMapping("/login/pre")
    public LoginResponse preLogin(@Valid @RequestBody LoginPreRequest loginRequest, HttpServletResponse response) {
        return authService.tryLogin(loginRequest, response);
    }

    /**
     * 用户登录,第一步：验证用户名密码，通过后返回临时token，用于第二步验证
     * 使用加密传输保护登录凭据
     */
    @NoAuth
    @PostMapping("/login/google")
    public LoginResponse login(@Valid @RequestBody LoginGoogleRequest loginRequest, HttpServletResponse response) {
        return authService.loginWithGoogle(loginRequest, response);
    }

    /**
     * 用户登录,第一步：验证用户名密码，通过后返回临时token，用于第二步验证
     * 使用加密传输保护登录凭据
     */
    @PostMapping("/login/check")
    public LoginResponse loginCheck(HttpServletResponse response) {
        return authService.loginCheck(response);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public void logout() {
        authService.logout();
    }


}