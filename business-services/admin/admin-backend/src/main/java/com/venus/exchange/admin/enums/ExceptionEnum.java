package com.venus.exchange.admin.enums;

import com.venus.framework.common.exception.BusinessExceptionEnum;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ExceptionEnum implements BusinessExceptionEnum {
    // 系统提示
    OPERATION_TIMEOUT(1000_0000, "Operation timeout, please login again"),
    ILLEGAL_REQUEST(1000_0001, "Illegal request"),
    // User 相关错误
    USER_EXISTS(1001_0001, "User already exists"),
    USER_NOT_EXISTS(1001_0002, "User does not exist"),
    USER_BANNED(1001_0003, "User is banned"),
    USER_FROZEN(1001_0004, "User is frozen due to too many failed attempts"),
    USER_WRONG_PASSWORD(1001_0005, "Incorrect password"),
    USER_WRONG_GOOGLE_CODE(1001_0006, "Incorrect google code"),

    // Role 相关错误
    ROLE_CODE_EXISTS(1001_1001, "Role code already exists"),
    ROLE_NOT_EXISTS(1001_1002, "Role does not exist"),

    // Permission 相关错误
    PERMISSION_SCANNING(1002_0001, "Permission scanning in progress. Please do not click repeatedly");

    private final int code;
    private final String msg;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
