package com.venus.exchange.admin.user.service.impl;


import com.venus.exchange.admin.enums.ExceptionEnum;
import com.venus.exchange.admin.system.entity.SysUser;
import com.venus.exchange.admin.system.service.ISysUserService;
import com.venus.exchange.admin.system.vo.UserResponseVO;
import com.venus.exchange.admin.system.vo.UserResponseVOMapper;
import com.venus.exchange.admin.user.service.IUserProfileService;
import com.venus.framework.common.exception.BusinessException;
import com.venus.framework.common.util.DefaultValueUtil;
import com.venus.framework.common.util.TotpUtil;
import com.venus.framework.web.context.RequestContext;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;



/**
 * 认证服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserProfileService implements IUserProfileService {

    private final ISysUserService userService;

    @Value("${venus.company.name: Exchange}")
    private String companyName;

    /**
     * 获取当前用户信息
     */
    public UserResponseVO getCurrentUser() {
        SysUser user = userService.getById(RequestContext.getUserId());
        if (user == null) {
            throw new BusinessException(ExceptionEnum.USER_NOT_EXISTS);
        }
        return UserResponseVOMapper.INSTANCE.toVO(user);
    }

    @Override
    public void generateGoogleSecret(HttpServletResponse response, int width, int height) {
        SysUser user = userService.getById(RequestContext.getUserId());
        if (user == null) {
            throw new BusinessException(ExceptionEnum.USER_NOT_EXISTS);
        }

        String secret = TotpUtil.generateBase32Secret();

        if (!StringUtils.hasText(secret)) {
            return;
        }

        user.setGoogleSecret(secret);
        userService.updateById(user);
    }
}
