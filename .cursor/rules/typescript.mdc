---
description: 面向现代 Web 开发的 TypeScript 编码规范与最佳实践
globs: **/*.ts, **/*.tsx, **/*.d.ts
alwaysApply: false
---

# TypeScript 最佳实践

## 类型系统
- 优先使用接口（interfaces）定义对象结构
- 使用类型（type）表示联合类型、交叉类型和映射类型
- 避免使用 `any`，未知类型时优先使用 `unknown`
- 使用严格的 TypeScript 配置
- 利用 TypeScript 内置的工具类型
- 使用泛型（generics）实现可复用的类型模式

## 命名规范
- 类型名称和接口使用 PascalCase
- 变量和函数使用 camelCase
- 常量使用 UPPER_CASE
- 使用带有辅助动词的描述性名称（例如：isLoading, hasError）
- React 属性接口名以 'Props' 为前缀（例如：ButtonProps）

## 代码组织
- 将类型定义放在接近其使用的位置
- 当类型被共享时，从专门的类型文件导出
- 使用 barrel 文件（index.ts）组织导出内容
- 将共享类型放在 `types` 目录中
- 将组件属性与其组件放在一起

## 函数
- 为公共函数指定明确的返回类型
- 回调函数和方法使用箭头函数
- 使用自定义错误类型实现正确的错误处理
- 在复杂类型场景中使用函数重载
- 优先使用 async/await 而不是 Promise

## 最佳实践
- 在 tsconfig.json 中启用 strict 模式
- 使用 readonly 表示不可变属性
- 使用可辨识联合类型提升类型安全性
- 使用类型守卫进行运行时类型检查
- 实现正确的 null 检查
- 除非必要，避免类型断言

## 错误处理
- 为领域特定错误创建自定义错误类型
- 对可能失败的操作使用 Result 类型
- 实现正确的错误边界
- 使用带类型捕获子句的 try-catch
- 正确处理 Promise 的拒绝（rejection）

## 设计模式
- 使用 Builder 模式构建复杂对象
- 使用 Repository 模式进行数据访问
- 使用 Factory 模式创建对象
- 使用依赖注入
- 使用 Module 模式实现封装