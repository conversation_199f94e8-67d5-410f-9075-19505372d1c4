---
description: 编写干净、可维护且易于人类阅读代码的指南。在编写或审查代码时应用这些规则，以确保一致性和代码质量。
globs: **/*.java
alwaysApply: false
---

# 干净代码指南

## 常量优于魔法数字（Constants Over Magic Numbers）
- 用命名常量替换硬编码的数值
- 使用能够说明值用途的描述性常量名
- 将常量按模块放在专门的常量文件中

## 意义明确的命名（Meaningful Names）
- 变量、函数和类名应体现其用途
- 名称应解释某个元素存在的原因及其使用方式
- 避免使用缩写，除非它们是广为人知的

## 聪明地使用注释（Smart Comments）
- 不要注释代码做了什么——让代码自文档化
- 使用注释来解释为何采用某种实现方式
- 记录 API、复杂算法以及非显而易见的副作用

## 单一职责原则（Single Responsibility）
- 每个函数只做一件事
- 函数应尽量小且功能集中
- 如果一个函数需要注释来解释其作用，则应该将其拆分

## 不重复原则（DRY, Don't Repeat Yourself）
- 将重复代码提取为可重用函数
- 通过合理抽象共享通用逻辑
- 维护单一的事实来源（即避免信息冗余）

## 清晰的结构（Clean Structure）
- 将相关的代码放在一起
- 按照逻辑层次组织代码
- 使用统一的文件和文件夹命名规范

## 封装（Encapsulation）
- 隐藏实现细节
- 暴露清晰的接口
- 将嵌套的条件判断移入具有良好命名的函数中

## 保持代码质量（Code Quality Maintenance）
- 持续进行代码重构
- 及早修复技术债务
- 离开时留下比你发现时更干净的代码

## 测试（Testing）
- 在修复 bug 前先编写测试
- 保持测试代码的可读性和可维护性
- 测试边界情况和错误条件

## 版本控制（Version Control）
- 编写清晰的提交信息
- 进行小而专注的提交
- 使用有意义的分支名称