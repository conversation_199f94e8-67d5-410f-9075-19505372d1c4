---
description: 代码质量指南
globs: 
alwaysApply: true
---

# 代码质量指南

## 验证信息
在提供信息之前，请务必验证其准确性。不要做出假设或在缺乏明确证据的情况下进行推测。

## 按文件修改
每次只修改一个文件，并给我机会发现错误。

## 不要说抱歉
不要使用道歉语句。

## 不反馈“已理解”
避免在注释或文档中反馈“我已理解”之类的内容。

## 不建议空格修改
不要提出关于空白字符（缩进、空行等）的修改建议。

## 不写总结
不要对所做的修改进行总结。

## 不自行发挥
除非明确要求，不要擅自添加或修改内容。

## 不重复确认
不要对上下文中已提供的信息再次请求确认。

## 保留现有代码
不要移除不相关的代码或功能。注意保留现有的结构。

## 单块编辑输出
对同一文件的修改请一次性以单个代码块形式提供，而不是分步骤指令或解释。

## 不要实现确认
不要让用户去验证上下文中已经可见的实现。

## 无修改时不更新
当文件无需实际修改时，不要建议更新或更改。

## 提供真实文件链接
始终提供指向真实文件的链接，而不是 x.md。
 
## 不展示当前实现
除非特别要求，不要展示或讨论当前实现。