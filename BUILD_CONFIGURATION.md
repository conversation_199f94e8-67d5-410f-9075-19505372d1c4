# 构建配置说明

## 概述

项目的构建配置已经统一提取到 `parent/pom.xml` 中，所有子模块都可以自动继承这些配置，无需重复配置。

## 自动继承的配置

### 1. 注解处理器配置
所有子模块自动继承以下注解处理器：
- **MapStruct**: `mapstruct-processor` - 用于生成 Bean 映射代码
- **Lombok**: `lombok` - 用于生成样板代码
- **Lombok-MapStruct绑定**: `lombok-mapstruct-binding` - 确保 Lombok 和 MapStruct 兼容
- **自定义注解处理器**: `venus-common` - 用于 `@EntityMapping` 注解处理

### 2. 插件版本管理
以下插件版本已在 parent 中统一管理：
- `maven-compiler-plugin`: 3.11.0
- `spring-boot-maven-plugin`: ${spring-boot.version}
- `native-maven-plugin`: 0.10.4
- `dockerfile-maven-plugin`: 1.4.13
- `maven-clean-plugin`: 3.4.0
- `maven-resources-plugin`: 3.3.1
- `maven-surefire-plugin`: 3.5.2
- `maven-jar-plugin`: 3.4.2
- `maven-install-plugin`: 3.1.3

## 子模块使用方式

### 基础模块（如 venus-common）
```xml
<project>
    <parent>
        <groupId>com.venus.exchange</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../../parent/pom.xml</relativePath>
    </parent>
    
    <artifactId>venus-common</artifactId>
    
    <!-- 无需额外的 build 配置，自动继承 parent 的配置 -->
</project>
```

### Spring Boot 应用模块（如 admin-backend）
```xml
<project>
    <parent>
        <groupId>com.venus.exchange</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../../../parent/pom.xml</relativePath>
    </parent>
    
    <artifactId>admin-backend</artifactId>
    
    <build>
        <plugins>
            <!-- 只需要声明需要的插件，无需配置 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            
            <!-- 可选：如果需要 Native Image 支持 -->
            <plugin>
                <groupId>org.graalvm.buildtools</groupId>
                <artifactId>native-maven-plugin</artifactId>
            </plugin>
            
            <!-- 可选：如果需要 Docker 支持 -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

## @EntityMapping 注解使用

现在所有模块都可以直接使用 `@EntityMapping` 注解：

```java
@Data
@EntityMapping(entity = SysUser.class)
public class UserResponseVO {
    private Long id;
    private String username;
    // ... 其他字段
}
```

编译时会自动生成：
- `UserResponseVOMapper.java` - 接口
- `UserResponseVOMapperImpl.java` - 实现类

使用方式：
```java
UserResponseVO vo = UserResponseVOMapper.INSTANCE.toVO(entity);
SysUser entity = UserResponseVOMapper.INSTANCE.toEntity(vo);
```

## 优势

1. **统一管理**: 所有构建配置在一个地方管理
2. **版本一致**: 确保所有模块使用相同版本的插件
3. **简化配置**: 子模块无需重复配置
4. **易于维护**: 升级插件版本只需修改 parent pom
5. **自动继承**: 新模块自动获得完整的构建能力

## 注意事项

1. 确保子模块的 `<parent>` 配置正确指向 parent pom
2. 如果子模块需要特殊配置，可以在子模块中覆盖
3. 所有使用 `@EntityMapping` 的模块都会自动生成对应的 Mapper
