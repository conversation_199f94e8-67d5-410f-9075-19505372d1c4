{"name": "exchanges-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"ant-design-vue": "^4.0.0", "axios": "^1.6.0", "pinia": "^2.1.0", "vue": "^3.3.0", "vue-router": "^4.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}}