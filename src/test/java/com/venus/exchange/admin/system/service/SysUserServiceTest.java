package com.venus.exchange.admin.system.service;

import com.venus.exchange.admin.system.vo.UserRequestVO;
import com.venus.exchange.admin.system.vo.UserResponseVO;
import com.venus.framework.mybatis.pagination.PaginationResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 用户服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class SysUserServiceTest {

    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private ISysDepartmentService departmentService;

    @Test
    public void testQueryUsersByDepartment() {
        // 测试查询指定部门的用户（包含子部门）
        UserRequestVO vo = new UserRequestVO();
        vo.setCurrentPage(1);
        vo.setPageSize(10);
        vo.setDeptId(1L); // 假设部门ID为1
        
        PaginationResult<UserResponseVO> result = userService.queryPage(vo);
        
        System.out.println("部门ID为1及其子部门的用户总数: " + result.getTotal());
        System.out.println("当前页用户数: " + (result.getList() != null ? result.getList().size() : 0));
        
        if (result.getList() != null) {
            result.getList().forEach(user -> {
                System.out.println("用户: " + user.getUsername() + 
                    ", 部门ID: " + user.getDeptId() + 
                    ", 部门名称: " + (user.getDepartment() != null ? user.getDepartment().getName() : "无"));
            });
        }
    }

    @Test
    public void testGetDepartmentAndChildrenIds() {
        // 测试获取部门及其子部门ID列表
        Long deptId = 1L; // 假设部门ID为1
        List<Long> deptIds = departmentService.getDepartmentAndChildrenIds(deptId);
        
        System.out.println("部门ID " + deptId + " 及其所有子部门的ID列表:");
        deptIds.forEach(id -> System.out.println("- " + id));
    }

    @Test
    public void testQueryUsersWithoutDepartmentFilter() {
        // 测试不带部门过滤的用户查询
        UserRequestVO vo = new UserRequestVO();
        vo.setCurrentPage(1);
        vo.setPageSize(5);
        
        PaginationResult<UserResponseVO> result = userService.queryPage(vo);
        
        System.out.println("所有用户总数: " + result.getTotal());
        System.out.println("当前页用户数: " + (result.getList() != null ? result.getList().size() : 0));
        
        if (result.getList() != null) {
            result.getList().forEach(user -> {
                System.out.println("用户: " + user.getUsername() + 
                    ", 部门ID: " + user.getDeptId() + 
                    ", 部门名称: " + (user.getDepartment() != null ? user.getDepartment().getName() : "无"));
            });
        }
    }

    @Test
    public void testQueryUsersWithMultipleFilters() {
        // 测试带多个过滤条件的用户查询
        UserRequestVO vo = new UserRequestVO();
        vo.setCurrentPage(1);
        vo.setPageSize(10);
        vo.setDeptId(1L); // 部门过滤
        vo.setUsername("admin"); // 用户名过滤
        vo.setStatus(0); // 状态过滤
        
        PaginationResult<UserResponseVO> result = userService.queryPage(vo);
        
        System.out.println("多条件过滤后的用户总数: " + result.getTotal());
        System.out.println("当前页用户数: " + (result.getList() != null ? result.getList().size() : 0));
        
        if (result.getList() != null) {
            result.getList().forEach(user -> {
                System.out.println("用户: " + user.getUsername() + 
                    ", 部门ID: " + user.getDeptId() + 
                    ", 状态: " + user.getStatus() +
                    ", 部门名称: " + (user.getDepartment() != null ? user.getDepartment().getName() : "无"));
            });
        }
    }
}
