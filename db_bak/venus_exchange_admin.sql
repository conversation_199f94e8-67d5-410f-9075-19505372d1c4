/*
 Navicat Premium Dump SQL

 Source Server         : mysql8
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : **************:3306
 Source Schema         : venus_exchange_admin

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 12/08/2025 13:35:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_sys_department
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_department`;
CREATE TABLE `t_sys_department` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父部门ID',
  `name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '部门名称',
  `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb3 COMMENT='部门表';

-- ----------------------------
-- Records of t_sys_department
-- ----------------------------
BEGIN;
INSERT INTO `t_sys_department` (`id`, `parent_id`, `name`, `sort`, `status`, `deleted`, `create_time`, `update_time`) VALUES (1, 3, 'dwc', 0, 0, b'0', '2025-06-23 02:58:39', '2025-07-16 16:17:44');
INSERT INTO `t_sys_department` (`id`, `parent_id`, `name`, `sort`, `status`, `deleted`, `create_time`, `update_time`) VALUES (2, 3, '深圳1', 1, 0, b'0', '2025-06-23 09:21:40', '2025-07-16 16:17:40');
INSERT INTO `t_sys_department` (`id`, `parent_id`, `name`, `sort`, `status`, `deleted`, `create_time`, `update_time`) VALUES (3, 0, '深1', 1, 0, b'0', '2025-06-23 09:22:21', '2025-07-16 16:10:20');
INSERT INTO `t_sys_department` (`id`, `parent_id`, `name`, `sort`, `status`, `deleted`, `create_time`, `update_time`) VALUES (9, 2, '阿第三方', 0, 0, b'0', '2025-07-17 22:10:25', '2025-07-17 22:10:25');
INSERT INTO `t_sys_department` (`id`, `parent_id`, `name`, `sort`, `status`, `deleted`, `create_time`, `update_time`) VALUES (11, 2, '啊啊啊', 0, 0, b'0', '2025-07-17 23:14:50', '2025-07-17 23:14:50');
INSERT INTO `t_sys_department` (`id`, `parent_id`, `name`, `sort`, `status`, `deleted`, `create_time`, `update_time`) VALUES (12, 0, '深圳', 0, 0, b'0', '2025-07-18 22:52:53', '2025-07-18 22:52:53');
INSERT INTO `t_sys_department` (`id`, `parent_id`, `name`, `sort`, `status`, `deleted`, `create_time`, `update_time`) VALUES (13, 9, 'bb', 0, 0, b'0', '2025-06-23 09:21:40', '2025-07-16 16:17:40');
COMMIT;

-- ----------------------------
-- Table structure for t_sys_i18n
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_i18n`;
CREATE TABLE `t_sys_i18n` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `app_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '',
  `language` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '时区',
  `code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '国际化key',
  `msg` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '国际化内容',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code_locale` (`language`,`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 COMMENT='国际化信息';

-- ----------------------------
-- Records of t_sys_i18n
-- ----------------------------
BEGIN;
INSERT INTO `t_sys_i18n` (`id`, `app_name`, `language`, `code`, `msg`) VALUES (3, 'admin', 'zh', '10010002', '用户不存在');
INSERT INTO `t_sys_i18n` (`id`, `app_name`, `language`, `code`, `msg`) VALUES (4, 'admin', 'en', '10010002', 'User does not exist');
COMMIT;

-- ----------------------------
-- Table structure for t_sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_menu`;
CREATE TABLE `t_sys_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '权限标识',
  `type` tinyint NOT NULL COMMENT '菜单类型',
  `sort` int NOT NULL DEFAULT '0' COMMENT '显示顺序',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父菜单ID',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '路由地址',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '菜单图标',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '菜单状态',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单权限表';

-- ----------------------------
-- Records of t_sys_menu
-- ----------------------------
BEGIN;
INSERT INTO `t_sys_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `deleted`, `create_time`, `update_time`) VALUES (1, '看板', 'dashboard', 1, 0, 0, '/dashboard', 'DashboardOutlined', 'dashboard', 0, b'0', '2025-07-24 00:56:21', '2025-07-27 16:19:53');
INSERT INTO `t_sys_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `deleted`, `create_time`, `update_time`) VALUES (2, '系统管理', 'sys', 0, 999, 0, '/system', 'SettingOutlined', NULL, 0, b'0', '2025-07-24 00:57:01', '2025-07-24 12:59:26');
INSERT INTO `t_sys_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `deleted`, `create_time`, `update_time`) VALUES (3, '部门管理', 'sys:dept', 1, 0, 2, '/system/dept', 'DeploymentUnitOutlined', 'system/dept', 0, b'0', '2025-07-24 00:57:31', '2025-07-27 16:16:06');
INSERT INTO `t_sys_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `deleted`, `create_time`, `update_time`) VALUES (4, '用户管理', 'sys:user', 1, 0, 2, '/system/user', 'UsergroupDeleteOutlined', 'system/user', 0, b'0', '2025-07-24 00:57:58', '2025-07-27 16:18:44');
INSERT INTO `t_sys_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `deleted`, `create_time`, `update_time`) VALUES (5, '菜单管理', 'sys:menu', 1, 0, 2, '/system/menu', 'MenuUnfoldOutlined', 'system/menu', 0, b'1', '2025-07-24 00:58:17', '2025-07-31 00:13:27');
INSERT INTO `t_sys_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `deleted`, `create_time`, `update_time`) VALUES (6, '角色管理', 'sys:role', 1, 0, 2, '/system/role', 'UserSwitchOutlined', 'system/role', 0, b'0', '2025-07-24 00:58:29', '2025-07-27 16:19:01');
INSERT INTO `t_sys_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `deleted`, `create_time`, `update_time`) VALUES (10, '会员管理', 'user:manage', 0, 0, 0, '/user/manage', 'UsergroupAddOutlined', '', 0, b'0', '2025-08-04 02:49:55', '2025-08-04 02:49:55');
INSERT INTO `t_sys_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `deleted`, `create_time`, `update_time`) VALUES (11, 'KYC', 'user:kyc', 0, 0, 10, '/user/kyc', 'CheckCircleOutlined', '', 0, b'0', '2025-08-04 02:53:35', '2025-08-04 02:53:35');
INSERT INTO `t_sys_menu` (`id`, `name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `deleted`, `create_time`, `update_time`) VALUES (12, '钱包管理', 'wallet:manage', 0, 1, 0, 'wallet/manage', 'WalletOutlined', '', 0, b'0', '2025-08-04 02:49:55', '2025-08-04 02:49:55');
COMMIT;

-- ----------------------------
-- Table structure for t_sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_permission`;
CREATE TABLE `t_sys_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '权限code',
  `desc` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '权限描述',
  `parent` varchar(100) NOT NULL DEFAULT '' COMMENT '父级权限，一般是定义在controller上的权限',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb3 COMMENT='权限表';

-- ----------------------------
-- Records of t_sys_permission
-- ----------------------------
BEGIN;
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (1, 'sys:permission:scan', '扫描权限', 'sys:permission');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (2, 'sys:permission', '权限管理', 'admin');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (11, 'sys:user', '用户管理', 'admin');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (12, 'sys:user:modify', '修改用户', 'sys:user');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (14, 'sys:user:create', '新增用户', 'sys:user');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (15, 'sys:user:del', '删除用户', 'sys:user');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (17, 'sys:dept:query', '查询部门', 'sys:dept');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (18, 'sys:dept:modify', '修改部门', 'sys:dept');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (19, 'sys:dept:create', '新增部门', 'sys:dept');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (20, 'sys:user:query', '查询用户', 'sys:user');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (21, 'sys:dept:del', '删除部门', 'sys:dept');
INSERT INTO `t_sys_permission` (`id`, `code`, `desc`, `parent`) VALUES (22, 'sys:dept', '部门管理', 'admin');
COMMIT;

-- ----------------------------
-- Table structure for t_sys_role
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_role`;
CREATE TABLE `t_sys_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '角色编码',
  `name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '角色名称',
  `desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '角色状态（0正常 1停用）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 COMMENT='角色信息表';

-- ----------------------------
-- Records of t_sys_role
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_role_menu`;
CREATE TABLE `t_sys_role_menu` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='角色和菜单关联表';

-- ----------------------------
-- Records of t_sys_role_menu
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_sys_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_role_permission`;
CREATE TABLE `t_sys_role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_code` varchar(100) NOT NULL DEFAULT '' COMMENT '权限code',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='角色权限表';

-- ----------------------------
-- Records of t_sys_role_permission
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_sys_user
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_user`;
CREATE TABLE `t_sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `nickname` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '昵称',
  `email` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '手机号码',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户状态（0正常 1停用）',
  `login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `login_time` datetime DEFAULT NULL COMMENT '登录成功时间',
  `login_attempt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `login_attempt_times` int DEFAULT '0' COMMENT '尝试登录次数',
  `password` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '密码',
  `google_secret` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '谷歌验证码密钥',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb3 COMMENT='用户信息表';

-- ----------------------------
-- Records of t_sys_user
-- ----------------------------
BEGIN;
INSERT INTO `t_sys_user` (`id`, `username`, `nickname`, `email`, `phone`, `dept_id`, `status`, `login_ip`, `login_time`, `login_attempt_time`, `login_attempt_times`, `password`, `google_secret`, `deleted`, `create_time`, `update_time`) VALUES (1, 'admin', '发所发生的发生的', '<EMAIL>', '13798368869', 1, 0, '127.0.0.1', '2025-07-31 17:35:33', '2025-07-24 00:19:44', 0, '$SHA-256$10000$Hzy8kqZvMb/43S+PuOBUDw==$i73RnAAUffjeE2xB2C/c/J8kUDkW+LTHyP46XkTw/rQ=', 'BQ366W52SYJ4B5E2XS5LKZPUJDFN7E2V', b'0', '2025-07-01 16:08:25', '2025-08-01 01:55:01');
INSERT INTO `t_sys_user` (`id`, `username`, `nickname`, `email`, `phone`, `dept_id`, `status`, `login_ip`, `login_time`, `login_attempt_time`, `login_attempt_times`, `password`, `google_secret`, `deleted`, `create_time`, `update_time`) VALUES (4, 'admin1', 'admin-nick', '<EMAIL>', '13798368882', 1, 0, NULL, NULL, '2025-07-05 04:35:28', 0, '$SHA-256$10000$VNeNQfwNy3RCkz5ulIzP5Q==$vwxiIfPzi2Y+MXs+z9n9OmzahDfPtcfurhk7SamjeCo=', '', b'0', '2025-07-02 23:08:52', '2025-08-01 01:54:31');
INSERT INTO `t_sys_user` (`id`, `username`, `nickname`, `email`, `phone`, `dept_id`, `status`, `login_ip`, `login_time`, `login_attempt_time`, `login_attempt_times`, `password`, `google_secret`, `deleted`, `create_time`, `update_time`) VALUES (5, 'admin2', 'admin-nick', '<EMAIL>', '13798368882', 3, 0, NULL, NULL, '2025-07-05 04:35:31', 0, 'asdf', '', b'0', '2025-07-04 01:42:31', '2025-07-16 16:37:15');
INSERT INTO `t_sys_user` (`id`, `username`, `nickname`, `email`, `phone`, `dept_id`, `status`, `login_ip`, `login_time`, `login_attempt_time`, `login_attempt_times`, `password`, `google_secret`, `deleted`, `create_time`, `update_time`) VALUES (7, 'admin3', 'admin-nick3', '<EMAIL>', '13798368882', 2, 1, NULL, '2025-07-08 04:23:10', '2025-07-08 04:23:10', 0, '$SHA-256$10000$uAogfzC32i27YsBKMpZunw==$8MPXiicgPGN6IDCt5REqXwb35iuOcD+dtOj+EgXIBsg=', '', b'0', '2025-07-04 02:01:53', '2025-07-16 16:38:41');
INSERT INTO `t_sys_user` (`id`, `username`, `nickname`, `email`, `phone`, `dept_id`, `status`, `login_ip`, `login_time`, `login_attempt_time`, `login_attempt_times`, `password`, `google_secret`, `deleted`, `create_time`, `update_time`) VALUES (8, 'admin4', 'adf', 'adsf', 'asdf', 12, 0, NULL, NULL, '2025-08-01 02:37:12', 0, '$SHA-256$10000$5GkKAEREUIRiE9i7+TQUbw==$qdDb7YS0gvv3q+D6DOiA9vX2Qy792C1HPllqgKOcaYc=', '', b'1', '2025-08-01 02:37:12', '2025-08-01 02:37:21');
INSERT INTO `t_sys_user` (`id`, `username`, `nickname`, `email`, `phone`, `dept_id`, `status`, `login_ip`, `login_time`, `login_attempt_time`, `login_attempt_times`, `password`, `google_secret`, `deleted`, `create_time`, `update_time`) VALUES (10, 'admin44', 'adf', 'adsf', 'adsf', 12, 0, NULL, NULL, '2025-08-01 02:38:53', 0, '$SHA-256$10000$fdi4q0ir2A2r4ffjTEeWzQ==$k7icoBjrAxjYWMOSJ/22TNzflV9zE1n2SMRpsaOrfBc=', '', b'1', '2025-08-01 02:38:53', '2025-08-01 02:38:56');
COMMIT;

-- ----------------------------
-- Table structure for t_sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_user_role`;
CREATE TABLE `t_sys_user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户和角色关联表';

-- ----------------------------
-- Records of t_sys_user_role
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
