spring:
  application:
    name: code-generator
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource # 指定使用Druid数据源
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,log4j2
      web-stat-filter:
        enabled: true
        url-pattern: /*
        excludes: '*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*'
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: 123456 # 请在生产环境中修改
        allow: # 允许访问的IP，为空则允许所有
        deny: