package com.venus.exchange;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import org.apache.ibatis.annotations.Mapper;

import java.sql.Types;
import java.util.Collections;

public abstract class GenBase {
    static {
        // 显式加载 MySQL 驱动
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("MySQL 驱动加载失败", e);
        }
    }

    private String url, username, password, app, module;
    private String[] tables;

    public void gen(String url, String username, String password, String app, String module, String[] tables) {
        this.url = url;
        this.username = username;
        this.password = password;
        this.app = app;
        this.module = module;
        this.tables = tables;
        execute();
    }

    private void execute() {
        String parentPackage = String.format("com.venus.exchange.%s", app);

        FastAutoGenerator.create(url, username, password)
                .globalConfig(builder -> {
                    builder.disableOpenDir()
//                    .enableSwagger()
                            .outputDir("./src/main/java"); // 指定输出目录
                })
                .dataSourceConfig(builder ->
                        builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                            if (typeCode == Types.SMALLINT) {
                                // 自定义类型转换
                                return DbColumnType.INTEGER;
                            }
                            return typeRegistry.getColumnType(metaInfo);
                        })
                )
                .packageConfig(builder ->
                        builder.parent(parentPackage) // 设置父包名
                                .moduleName(module) // 设置父包模块名
                                .pathInfo(Collections.singletonMap(OutputFile.xml, "./src/main/resources/mapper/" + module)) // 设置mapperXml生成路径
                )
                .strategyConfig(builder ->
                                builder.addInclude(tables) // 设置需要生成的表名
                                        .addTablePrefix("t_") // 设置过滤表前缀
                                        .entityBuilder()
//                                        .javaTemplate("/templates/entity.java") // 设置实体类模板
                                        .enableLombok()
//                                        .addClassAnnotation(new ClassAnnotationAttributes(Data.class))
                                        .mapperBuilder()
                                        .disableMapperXml()
                                        .mapperAnnotation(Mapper.class)
//                                .mapperTemplate("/templates/mapper.java")
//                                .mapperXmlTemplate("/templates/mapper.xml")
                                        .serviceBuilder()
//                                .serviceTemplate("/templates/service.java") // 设置 Service 模板
//                                .serviceImplTemplate("/templates/serviceImpl.java") // 设置 ServiceImpl 模板
                                        .controllerBuilder()
//                                .template("/templates/controller.java")
                                        .enableRestStyle() // 启用 REST 风格

                )
//                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }

}
