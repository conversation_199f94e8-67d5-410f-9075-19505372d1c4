package com.venus.framework.web.security.session;

import com.venus.framework.common.enums.DeviceTypeEnum;
import com.venus.framework.common.security.session.IAuthSessionHandler;
import com.venus.framework.common.security.session.Session;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

/**
 * session在本地缓存
 */
@AutoConfiguration
@ConditionalOnProperty(name = "venus.security.session.handler", havingValue = "local", matchIfMissing = true)
public class LocalAuthSessionHandler implements IAuthSessionHandler {

    @Override
    public Session getSession(String appName, DeviceTypeEnum deviceType, String sessionId) {
        return null;
    }

    @Override
    public void saveSession(String appName, DeviceTypeEnum deviceType, Session session) {

    }

    @Override
    public void removeSession(String appName, DeviceTypeEnum deviceType, String sessionId) {

    }
}
