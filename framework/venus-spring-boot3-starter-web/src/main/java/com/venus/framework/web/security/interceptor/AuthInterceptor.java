package com.venus.framework.web.security.interceptor;

import com.venus.framework.common.security.annotation.NoAuth;
import com.venus.framework.common.security.annotation.Permission;
import com.venus.framework.common.security.session.IAuthSessionHandler;
import com.venus.framework.common.security.session.Session;
import com.venus.framework.web.context.RequestContext;
import com.venus.framework.web.security.constants.WebSecurityConst;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 认证拦截器
 */
@Slf4j
@AutoConfiguration
@RequiredArgsConstructor
public class AuthInterceptor implements HandlerInterceptor, EnvironmentAware {

    private final IAuthSessionHandler sessionHandler;
    private Environment environment;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果不是HandlerMethod，直接放行
        if (!(handler instanceof HandlerMethod handlerMethod)) {
            return true;
        }

        Class<?> clazz = handlerMethod.getBean().getClass();
        // 设置上下文
        String appName = environment.getProperty("spring.application.name");
        RequestContext context = RequestContext.getCurrentContext();
        context.setRequest(request);
        context.setAppName(appName);

        // 无需认证 注解标记的直接放行，用于放行登录等接口
        NoAuth noAuth = handlerMethod.getMethodAnnotation(NoAuth.class);
        if (clazz.isAnnotationPresent(NoAuth.class) || noAuth != null) {
            return true;
        }

        // 身份认证
        // 获取请求头
        String sessionId = request.getHeader(WebSecurityConst.HEADER_SESSION_ID);
        if (!StringUtils.hasText(sessionId)) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("{\"code\":401,\"message\":\"未授权访问\"}");
            return false;
        }

        // 从redis或者本地缓存中获取session
        Session session = sessionHandler.getSession(appName, RequestContext.getDeviceType(), sessionId);
        if (session == null) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("{\"code\":401,\"message\":\"身份认证已失效\"}");
            return false;
        }
        context.setSession(session);

        // 鉴权，方法权限和类权限拥有任意一个即可，在方法上的鉴权是短路原理，意在简化用户的权限数据
        // 鉴权通过的场景：
        //  1.拥有整个应用的权限，权限名：{spring.application.name}
        //  1.类和方法上没有权限注解，表示无需鉴权，直接通过
        //  2.类和方法上任意一个有权限注解，且用户拥有其权限，则通过
        if (session.hasPermission("*")) {
            return true;
        }

        Permission methodPermission = handlerMethod.getMethodAnnotation(Permission.class);
        Permission classPermission = clazz.getAnnotation(Permission.class);
        if (!((methodPermission == null && classPermission == null) ||
                (classPermission != null && session.hasPermission(classPermission.code())) ||
                (methodPermission != null && session.hasPermission(methodPermission.code())))
        ) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.getWriter().write("{\"code\":403,\"message\":\"未授权访问\"}");
            return false;
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        RequestContext.clear();
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }
}