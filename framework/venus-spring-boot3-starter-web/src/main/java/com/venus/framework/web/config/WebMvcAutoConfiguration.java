package com.venus.framework.web.config;

import com.venus.framework.web.core.response.DefaultI18nTranslator;
import com.venus.framework.web.core.response.GlobalExceptionHandler;
import com.venus.framework.web.core.response.GlobalResponseWrapper;
import com.venus.framework.web.runner.EnvironmentChecker;
import com.venus.framework.web.security.interceptor.AuthInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@AutoConfiguration
@RequiredArgsConstructor
@Import({GlobalExceptionHandler.class,
        GlobalResponseWrapper.class,
        EnvironmentChecker.class,
        AuthInterceptor.class,
        DefaultI18nTranslator.class})
public class WebMvcAutoConfiguration implements WebMvcConfigurer {

    private final AuthInterceptor authInterceptor;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/**");
    }
}
