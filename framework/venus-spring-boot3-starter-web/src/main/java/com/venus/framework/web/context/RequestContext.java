package com.venus.framework.web.context;

import com.venus.framework.common.enums.DeviceTypeEnum;
import com.venus.framework.common.security.session.Session;
import com.venus.framework.web.security.constants.WebSecurityConst;
import com.venus.framework.web.utils.IpUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;

/**
 * Venus 上下文
 */
@Getter
@Setter
public class RequestContext {
    private static final ThreadLocal<RequestContext> CONTEXT_HOLDER = new ThreadLocal<>();
    private String appName;

    private Session session;

    private HttpServletRequest request;


    /**
     * 获取当前上下文
     */
    public static RequestContext getCurrentContext() {
        RequestContext context = CONTEXT_HOLDER.get();
        if (context == null) {
            context = new RequestContext();
            CONTEXT_HOLDER.set(context);
        }
        return context;
    }

    /**
     * 清除上下文
     */
    public static void clear() {
        CONTEXT_HOLDER.remove();
    }

    public static long getUserId() {
        Session session = RequestContext.getCurrentContext().session;
        return session == null ? 0L : session.getUserId();
    }

    public static String getSessionId() {
        String sessionId = RequestContext.getCurrentContext().request.getHeader(WebSecurityConst.HEADER_SESSION_ID);
        if (StringUtils.hasText(sessionId)) {
            return sessionId;
        }
        Session session = RequestContext.getCurrentContext().session;
        return session == null ? null : session.getId();
    }

    public static DeviceTypeEnum getDeviceType() {
        String deviceType = RequestContext.getCurrentContext().request.getHeader(WebSecurityConst.HEADER_DEVICE_TYPE);
        return StringUtils.hasText(deviceType) ? DeviceTypeEnum.fromCode(deviceType) : DeviceTypeEnum.BROWSER;
    }

    public static String getAppName() {
        return getCurrentContext().appName;
    }

    public static String getLanguage() {
        String language = getCurrentContext().request.getLocale().getLanguage();
        return StringUtils.hasText(language) ? language : "en";
    }

    public static String getClientIp() {
        return IpUtil.getClientIp(getCurrentContext().request);
    }

}