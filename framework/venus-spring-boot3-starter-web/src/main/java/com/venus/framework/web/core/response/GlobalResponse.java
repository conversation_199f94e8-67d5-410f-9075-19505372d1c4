package com.venus.framework.web.core.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 通用响应
 */
@Getter
@Setter
public class GlobalResponse<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = -2568706754953547727L;

    private String code;
    private T data;
    private String msg;

    public static <T> GlobalResponse<T> success(T data) {
        GlobalResponse<T> result = new GlobalResponse<>();
        result.code = "0";
        result.msg = "success";
        result.data = data;
        return result;
    }

    public static <T> GlobalResponse<T> error(String code, String msg) {
        GlobalResponse<T> result = new GlobalResponse<>();
        result.code = code;
        result.msg = msg;
        return result;
    }

    public static <T> GlobalResponse<T> error(Throwable cause) {
        GlobalResponse<T> result = new GlobalResponse<>();
        result.code = "500";
        result.msg = cause.getMessage();
        return result;
    }
}
