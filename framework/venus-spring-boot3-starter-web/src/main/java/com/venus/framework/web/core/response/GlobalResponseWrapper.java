package com.venus.framework.web.core.response;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@ControllerAdvice
public class GlobalResponseWrapper implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        Class<?> paramType = returnType.getParameterType();
        return !paramType.getName().startsWith("org.springframework")
                && paramType != GlobalResponse.class;
    }

    /**
     * 统一返回
     */
    @Override
    public Object beforeBodyWrite(Object body,
                                  MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {

        // 如果已经是GlobalResponse类型，直接返回
        if (body instanceof GlobalResponse) {
            return body;
        }

        // 从 response 中获取真实的状态码
        HttpServletResponse servletResponse = ((ServletServerHttpResponse) response).getServletResponse();
        if (servletResponse.getStatus() != HttpStatus.OK.value()) {
            return body;
        }

        return GlobalResponse.success(body); // 普通数据包装
    }
}