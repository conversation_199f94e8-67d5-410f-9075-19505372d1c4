package com.venus.framework.web.utils;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;

public class IpUtil {
    private static final String[] HTTP_IP_HEADERS = {
            "X-Forwarded-For",// 代理服务器（如 Nginx、Apache），格式：X-Forwarded-For: client, proxy1, proxy2
            "HTTP_X_FORWARDED_FOR", // 同 X-Forwarded-For
            "HTTP_CLIENT_IP", // 非标准，但可能用到
            "Proxy-Client-IP",// 代理服务器（例如 IBM WebSphere 插件）添加
            "WL-Proxy-Client-IP"// 由 Oracle WebLogic Server 或者其代理插件设置
    };
    private static final String IP_UNKNOWN = "unknown";

    public static String getClientIp(HttpServletRequest request) {
        String ip = null;
        for (String header : HTTP_IP_HEADERS) {
            ip = request.getHeader(header);
            if (StringUtils.hasText(ip) && !IP_UNKNOWN.equalsIgnoreCase(ip)) {
                break;
            }
        }
        if (!StringUtils.hasText(ip) || IP_UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果是多级代理，那么取第一个非 unknown 的 IP。
        if (StringUtils.hasText(ip) && ip.contains(",")) {
            String[] ips = ip.split(",");
            for (String subIp : ips) {
                subIp = subIp.trim();
                if (!IP_UNKNOWN.equalsIgnoreCase(subIp)) {
                    ip = subIp;
                    break;
                }
            }
        }

        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }
        return ip;
    }
}
