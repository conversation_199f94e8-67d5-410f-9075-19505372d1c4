package com.venus.framework.web.security.permission;

import com.venus.framework.common.permission.CommonPermission;
import com.venus.framework.common.security.annotation.Permission;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;


@AutoConfiguration
@ConditionalOnProperty(name = "venus.security.permission-scan.enable", havingValue = "true")
public class PermissionScanner implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public synchronized HashMap<String, CommonPermission> scanPermissionsFromCode() {
        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(RestController.class); // 可以按需调整扫描范围
        HashMap<String, CommonPermission> permissionsFromCode = new HashMap<>();

        // 系统级别权限
        permissionsFromCode.put(applicationName, new CommonPermission(applicationName, applicationName, ""));

        for (Object bean : beans.values()) {
            Class<?> clazz = AopProxyUtils.ultimateTargetClass(bean);

            String classAnnoCode = "";
            // 类级别的注解
            if (clazz.isAnnotationPresent(Permission.class)) {
                Permission classAnno = clazz.getAnnotation(Permission.class);
                classAnnoCode = classAnno.code();
                permissionsFromCode.put(classAnnoCode, new CommonPermission(classAnnoCode, classAnno.desc(), applicationName));
            }

            // 方法级别的注解
            for (Method method : clazz.getDeclaredMethods()) {
                if (method.isAnnotationPresent(Permission.class)) {
                    Permission methodAnno = method.getAnnotation(Permission.class);
                    permissionsFromCode.put(methodAnno.code(), new CommonPermission(methodAnno.code(), methodAnno.desc(), classAnnoCode));
                }
            }
        }
        return permissionsFromCode;
    }


}
