package com.venus.framework.web.runner;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;

@AutoConfiguration
public class EnvironmentChecker implements ApplicationRunner, EnvironmentAware {
    private Environment environment;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        Assert.notNull(environment.getProperty("spring.application.name"), "spring.application.name is required");// 根权等功能要用到
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

}
