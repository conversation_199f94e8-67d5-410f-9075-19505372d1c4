package com.venus.framework.web.core.response;

import com.venus.framework.common.i18n.II18nTranslator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;

@Slf4j
@AutoConfiguration
public class DefaultI18nTranslator implements II18nTranslator {
    @Override
    public String translate(String code, String defaultMsg) {
        log.info("Using default i18n translator");
        return defaultMsg;
    }
}
