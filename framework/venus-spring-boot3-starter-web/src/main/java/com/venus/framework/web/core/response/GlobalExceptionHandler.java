package com.venus.framework.web.core.response;

import com.venus.framework.common.exception.BusinessException;
import com.venus.framework.common.i18n.II18nTranslator;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@ControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {
    private final II18nTranslator translator;

    /**
     * 重写父类的方法来处理 @Valid 验证失败异常
     * 优化：将国际化错误消息直接赋值给 ApiResponse 的 msg 字段
     * 如果有多个验证错误就用逗号分隔
     */
    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        StringBuilder errorMessages = new StringBuilder();

        // 收集所有字段错误，并用逗号分隔组合错误消息
        for (var error : ex.getBindingResult().getAllErrors()) {
            String errorMessage = error.getDefaultMessage();
            if (!errorMessages.isEmpty()) {
                errorMessages.append(", ");
            }
            errorMessages.append(errorMessage);
        }
        return new ResponseEntity<>(GlobalResponse.error("400", errorMessages.isEmpty() ? "Validation failed" : errorMessages.toString()), HttpStatus.OK);
    }

    @ExceptionHandler(value = {BusinessException.class})
    protected ResponseEntity<GlobalResponse<?>> handleBusinessException(BusinessException ex, WebRequest request) {
        String code = String.valueOf(ex.getCode());
        return new ResponseEntity<>(GlobalResponse.error(code, translator.translate(code, ex.getMsg())), HttpStatus.OK);
    }

    @ExceptionHandler(value = {Exception.class})
    protected ResponseEntity<GlobalResponse<?>> handleGeneralException(Exception ex, WebRequest request) {
        return new ResponseEntity<>(GlobalResponse.error(ex), HttpStatus.INTERNAL_SERVER_ERROR);
    }
}