package com.venus.framework.common.mapstruct;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class MapStructTest {

    @Test
    public void testEntityMappingAnnotation() {
        // 创建测试实体
        TestEntity entity = new TestEntity();
        entity.setId(1L);
        entity.setName("Test User");
        entity.setEmail("<EMAIL>");

        // 转换为 VO - 使用自动生成的 Mapper
        TestVO vo = TestVOMapper.INSTANCE.toVO(entity);

        // 验证转换结果
        assertNotNull(vo);
        assertEquals(entity.getId(), vo.getId());
        assertEquals(entity.getName(), vo.getName());
        assertEquals(entity.getEmail(), vo.getEmail());

        // 转换回实体
        TestEntity convertedEntity = TestVOMapper.INSTANCE.toEntity(vo);

        // 验证转换结果
        assertNotNull(convertedEntity);
        assertEquals(entity.getId(), convertedEntity.getId());
        assertEquals(entity.getName(), convertedEntity.getName());
        assertEquals(entity.getEmail(), convertedEntity.getEmail());
    }
}
