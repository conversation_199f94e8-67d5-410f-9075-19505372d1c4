package com.venus.framework.common.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码工具类
 * 提供密码加密和验证功能
 */
@Slf4j
public class PasswordUtil {
    
    private static final String ALGORITHM = "SHA-256";
    private static final int SALT_LENGTH = 16;
    private static final int ITERATIONS = 10000;
    
    /**
     * 生成随机盐值
     *
     * @return Base64编码的盐值
     */
    public static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[SALT_LENGTH];
        random.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }
    
    /**
     * 使用PBKDF2算法加密密码
     *
     * @param password 原始密码
     * @param salt     盐值
     * @return 加密后的密码
     */
    public static String encryptPassword(String password, String salt) {
        try {
            byte[] saltBytes = Base64.getDecoder().decode(salt);
            
            // 使用PBKDF2算法
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            md.update(saltBytes);
            
            byte[] passwordBytes = password.getBytes(StandardCharsets.UTF_8);
            byte[] hashedPassword = passwordBytes;
            
            // 多次迭代增强安全性
            for (int i = 0; i < ITERATIONS; i++) {
                md.reset();
                md.update(saltBytes);
                md.update(hashedPassword);
                hashedPassword = md.digest();
            }
            
            return Base64.getEncoder().encodeToString(hashedPassword);
        } catch (Exception e) {
            log.error("密码加密失败", e);
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * 生成完整的密码哈希（包含盐值）
     * 格式：$algorithm$iterations$salt$hash
     *
     * @param password 原始密码
     * @return 完整的密码哈希
     */
    public static String hashPassword(String password) {
        String salt = generateSalt();
        String hash = encryptPassword(password, salt);
        return String.format("$%s$%d$%s$%s", ALGORITHM, ITERATIONS, salt, hash);
    }
    
    /**
     * 验证密码
     *
     * @param password     原始密码
     * @param hashedPassword 存储的哈希密码
     * @return 是否匹配
     */
    public static boolean verifyPassword(String password, String hashedPassword) {
        try {
            // 解析哈希密码格式：$algorithm$iterations$salt$hash
            String[] parts = hashedPassword.split("\\$");
            if (parts.length != 5) {
                log.warn("密码哈希格式不正确: {}", hashedPassword);
                return false;
            }
            
            String algorithm = parts[1];
            int iterations = Integer.parseInt(parts[2]);
            String salt = parts[3];
            String expectedHash = parts[4];
            
            // 验证算法和迭代次数
            if (!ALGORITHM.equals(algorithm) || iterations != ITERATIONS) {
                log.warn("密码哈希算法或迭代次数不匹配");
                return false;
            }
            
            // 使用相同的盐值加密输入密码
            String actualHash = encryptPassword(password, salt);
            
            // 比较哈希值
            return expectedHash.equals(actualHash);
        } catch (Exception e) {
            log.error("密码验证失败", e);
            return false;
        }
    }
}
