package com.venus.framework.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum MenuTypeEnum {
    DIR(0, "目录"),
    MENU(1, "菜单"),
    BUTTON(2, "按钮");

    @EnumValue
    @JsonValue
    private final Integer type;
    private final String description;

    /**
     * 根据 code 获取对应的枚举值
     */
    public static MenuTypeEnum fromCode(Integer type) {
        return Stream.of(values())
                .filter(e -> e.getType().equals(type))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid type: " + type));
    }
}