package com.venus.framework.common.exception;

import lombok.Getter;

import java.io.Serial;


@Getter
public class BusinessException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = -6414477996365739388L;

    private final int code;
    private final String msg;

    public BusinessException(BusinessExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMsg());
        this.code = exceptionEnum.getCode();
        this.msg = exceptionEnum.getMsg();
    }
}
