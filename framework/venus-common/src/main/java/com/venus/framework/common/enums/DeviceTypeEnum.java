package com.venus.framework.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum DeviceTypeEnum {

    APP("mobile", "移动端"),
    BROWSER("browser", "浏览器");

    /**
     * 状态值
     */
    @EnumValue
    @JsonValue
    private final String code;
    /**
     * 状态名
     */
    private final String name;

    /**
     * 根据 code 获取对应的枚举
     */
    public static DeviceTypeEnum fromCode(String code) {
        return Stream.of(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid code: " + code));
    }
}
