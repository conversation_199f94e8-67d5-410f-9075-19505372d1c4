package com.venus.framework.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum UserStatusEnum {
    NORMAL(0, "正常"),
    BANNED(1, "禁用"),
    FROZEN(2, "密码错误次数上限冻结");

    @EnumValue
    @JsonValue
    private final Integer status;
    private final String description;

    /**
     * 根据 code 获取对应的枚举值
     */
    public static UserStatusEnum fromCode(Integer status) {
        return Stream.of(values())
                .filter(e -> e.getStatus().equals(status))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid status: " + status));
    }
}