package com.venus.framework.common.mapstruct;

import javax.annotation.processing.*;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.TypeElement;
import javax.tools.Diagnostic;
import javax.tools.JavaFileObject;
import java.io.PrintWriter;
import java.util.Set;

@SupportedAnnotationTypes("com.venus.framework.common.mapstruct.EntityMapping")
@SupportedSourceVersion(SourceVersion.RELEASE_21)
public class EntityMappingProcessor extends AbstractProcessor {
    private Messager messager;

    @Override
    public synchronized void init(ProcessingEnvironment processingEnv) {
        super.init(processingEnv);
        this.messager = processingEnv.getMessager();
    }

    @Override
    public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment roundEnv) {
        messager.printMessage(Diagnostic.Kind.NOTE, "开始处理注解...");

        for (TypeElement annotation : annotations) {
            for (Element element : roundEnv.getElementsAnnotatedWith(annotation)) {
                if (element instanceof TypeElement voClass) {
                    try {
                        // 获取注解上的 target 类型
                        var annotationMirror = element.getAnnotationMirrors().stream()
                                .filter(am -> am.getAnnotationType().toString().equals(EntityMapping.class.getName()))
                                .findFirst().orElseThrow();

                        var targetEntity = (TypeElement) ((javax.lang.model.type.DeclaredType) annotationMirror.getElementValues().values().iterator().next().getValue()).asElement();

                        String voClassName = voClass.getSimpleName().toString();
                        String entityClassName = targetEntity.getSimpleName().toString();
                        String entityFullName = targetEntity.getQualifiedName().toString();
                        String packageName = processingEnv.getElementUtils().getPackageOf(voClass).getQualifiedName().toString();

                        // 生成 Mapper 接口代码
                        String mapperCode = String.format("""
                                        package %s;
                                        
                                        import %s;
                                        import org.mapstruct.Mapper;
                                        import org.mapstruct.ReportingPolicy;
                                        import org.mapstruct.factory.Mappers;
                                        
                                        @Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
                                        public interface %sMapper {
                                            %sMapper INSTANCE = Mappers.getMapper(%sMapper.class);
                                        
                                            %s toVO(%s entity);
                                            %s toEntity(%s vo);
                                        }
                                        """,
                                packageName,
                                entityFullName,
                                voClassName,
                                voClassName,
                                voClassName,
                                voClassName, entityClassName,
                                entityClassName, voClassName
                        );

                        // 写入文件
                        JavaFileObject builderFile = processingEnv.getFiler()
                                .createSourceFile(packageName + "." + voClassName + "Mapper");
                        try (PrintWriter out = new PrintWriter(builderFile.openWriter())) {
                            out.println(mapperCode);
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return true;
    }
}