package com.venus.framework.common.security.session;

import com.venus.framework.common.enums.DeviceTypeEnum;

public interface IAuthSessionHandler {
    Session getSession(String appName, DeviceTypeEnum deviceType, String sessionId);

    void saveSession(String appName, DeviceTypeEnum deviceType, Session session);

    void removeSession(String appName, DeviceTypeEnum deviceType, String sessionId);
}
