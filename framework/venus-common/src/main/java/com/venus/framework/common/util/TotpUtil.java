package com.venus.framework.common.util;

import com.eatthepath.otp.TimeBasedOneTimePasswordGenerator;
import com.google.common.io.BaseEncoding;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import jakarta.servlet.http.HttpServletResponse;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.SecureRandom;
import java.time.Instant;
import java.util.Base64;
import java.util.EnumMap;
import java.util.Map;

public class TotpUtil {

    private static final TimeBasedOneTimePasswordGenerator totpGenerator =
            new TimeBasedOneTimePasswordGenerator();

    /**
     * 生成一个 Base32 编码的随机 Secret
     */
    public static String generateBase32Secret() {
        byte[] secretBytes = new byte[20]; // 160 bits for SHA1
        new SecureRandom().nextBytes(secretBytes);
        return BaseEncoding.base32().encode(secretBytes);
    }

    /**
     * 生成并输出 QR Code 图像
     *
     * @param secret  TOTP 密钥
     * @param account 用户账号名称（显示在 Google Authenticator 中）
     * @param issuer  应用名称（显示在 Google Authenticator 中）
     * @param width   二维码宽度
     * @param height  二维码高度
     */
    public static BitMatrix generateQrCodeImage(String secret, String account, String issuer, int width, int height)
            throws WriterException {
        String otpAuthUrl = String.format("otpauth://totp/%s:%s?secret=%s&issuer=%s",
                issuer, account, secret, issuer);

        Map<EncodeHintType, Object> hints = new EnumMap<>(EncodeHintType.class);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);
        hints.put(EncodeHintType.MARGIN, 1);

        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        return qrCodeWriter.encode(otpAuthUrl, BarcodeFormat.QR_CODE, width, height, hints);
    }

    /**
     * 生成并输出Base64 QR Code
     *
     * @param secret  TOTP 密钥
     * @param account 用户账号名称（显示在 Google Authenticator 中）
     * @param issuer  应用名称（显示在 Google Authenticator 中）
     * @param width   二维码宽度
     * @param height  二维码高度
     */
    public static String generateBase64QrCodeImage(String secret, String account, String issuer, int width, int height)
            throws WriterException, IOException {
        BitMatrix bitMatrix = generateQrCodeImage(secret, account, issuer, width, height);
        BufferedImage bufferedImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
        ByteArrayOutputStream imageOutStream = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage, "png", imageOutStream);
        byte[] imageBytes = imageOutStream.toByteArray();

        // Step 4: Encode byte array to Base64
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 生成并输出 QR Code 图像
     *
     * @param account 用户账号名称（显示在 Google Authenticator 中）
     * @param issuer  应用名称（显示在 Google Authenticator 中）
     * @param width   二维码宽度
     * @param height  二维码高度
     */
    public static BitMatrix generateQrCodeImage(String account, String issuer, int width, int height)
            throws WriterException {
        String secret = TotpUtil.generateBase32Secret();
        return generateQrCodeImage(secret, account, issuer, width, height);
    }

    /**
     * 生成并输出 QR Code 到 HttpServletResponse，供前端扫码绑定
     *
     * @param response http响应
     * @param account  用户账号名称（显示在 Google Authenticator 中）
     * @param issuer   应用名称（显示在 Google Authenticator 中）
     * @param width    二维码宽度
     * @param height   二维码高度
     */
    public static String generateSecretAndWriteQrCodeImage(HttpServletResponse response, String account, String issuer, int width, int height)
            throws WriterException, IOException {
        String secret = generateBase32Secret();
        BitMatrix bitMatrix = generateQrCodeImage(secret, account, issuer, width, height);
        response.setContentType("image/png");
        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", response.getOutputStream());
        return secret;
    }

    /**
     * 验证用户输入的 TOTP 是否与当前时间匹配
     *
     * @param secret 用户的密钥
     * @param code   用户输入的一次性密码
     * @return 是否有效
     */
    public static boolean verifyCode(String secret, String code) {
        try {
            byte[] decodedKey = BaseEncoding.base32().decode(secret);
            SecretKey secretKey = new SecretKeySpec(decodedKey, "RAW");

            Instant now = Instant.now();

            // 允许 ±1 时间步长（默认是30秒），防止时钟偏移问题
            for (int i = -1; i <= 1; i++) {
                Instant adjustedTime = now.plusSeconds(i * totpGenerator.getTimeStep().getSeconds());
                int generatedCode = totpGenerator.generateOneTimePassword(secretKey, adjustedTime);
                String formattedCode = String.format("%06d", generatedCode);

                if (formattedCode.equals(code)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public static String generateCodeTest(String secret) throws InvalidKeyException {
        byte[] decodedKey = BaseEncoding.base32().decode(secret);
        SecretKey secretKey = new SecretKeySpec(decodedKey, "RAW");
        Instant adjustedTime = Instant.now().plusSeconds(totpGenerator.getTimeStep().getSeconds());
        int generatedCode = totpGenerator.generateOneTimePassword(secretKey, adjustedTime);
        return String.format("%06d", generatedCode);
    }
}
