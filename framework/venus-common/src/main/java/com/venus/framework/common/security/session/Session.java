package com.venus.framework.common.security.session;

import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashSet;
import java.util.UUID;

@Getter
@Setter
public class Session implements Serializable {

    @Serial
    private static final long serialVersionUID = 2481829350918472661L;

    private long userId;

    private String id;

//    private HashSet<String> roles;

    private HashSet<String> permissions;

    private Session() {
    }

    public static Session create() {
        Session session = new Session();
        session.setId(UUID.randomUUID().toString().replace("-", ""));
        return session;
    }

    /**
     * 判断当前用户是否有指定权限
     */
    public boolean hasPermission(String permission) {
        if (!StringUtils.hasText(permission)) {
            return false;
        }

        if (CollectionUtils.isEmpty(permissions)) {
            return false;
        }

        return permissions.contains(permission);
    }

}
