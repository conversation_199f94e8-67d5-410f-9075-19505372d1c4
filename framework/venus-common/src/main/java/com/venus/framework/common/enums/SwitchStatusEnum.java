package com.venus.framework.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum SwitchStatusEnum {

    ON(0, "开启/启用"),
    OFF(1, "关闭/禁用");

    /**
     * 状态值
     */
    @EnumValue
    @JsonValue
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    public static SwitchStatusEnum fromCode(Integer status) {
        return Stream.of(values())
                .filter(e -> e.getStatus().equals(status))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid status: " + status));
    }
}
