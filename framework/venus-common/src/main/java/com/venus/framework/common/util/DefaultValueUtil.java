package com.venus.framework.common.util;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * 零值判断工具类
 * 如果值为零值（null、0、空字符串等），则返回默认值
 */
public class DefaultValueUtil {

    /**
     * 如果字符串为null或空，返回默认值
     */
    public static String defaultIfEmpty(String value, String defaultValue) {
        return (value == null || value.isEmpty()) ? defaultValue : value;
    }

    /**
     * 如果字符串为null或空白字符，返回默认值
     */
    public static String defaultIfBlank(String value, String defaultValue) {
        return (value == null || value.trim().isEmpty()) ? defaultValue : value;
    }

    // ===== 基本数据类型处理 =====

    /**
     * 如果int为0，返回默认值
     */
    public static int defaultIfZero(int value, int defaultValue) {
        return value == 0 ? defaultValue : value;
    }

    /**
     * 如果long为0，返回默认值
     */
    public static long defaultIfZero(long value, long defaultValue) {
        return value == 0L ? defaultValue : value;
    }

    /**
     * 如果float为0，返回默认值
     */
    public static float defaultIfZero(float value, float defaultValue) {
        return value == 0.0f ? defaultValue : value;
    }

    /**
     * 如果double为0，返回默认值
     */
    public static double defaultIfZero(double value, double defaultValue) {
        return value == 0.0 ? defaultValue : value;
    }

    /**
     * 如果byte为0，返回默认值
     */
    public static byte defaultIfZero(byte value, byte defaultValue) {
        return value == 0 ? defaultValue : value;
    }

    /**
     * 如果short为0，返回默认值
     */
    public static short defaultIfZero(short value, short defaultValue) {
        return value == 0 ? defaultValue : value;
    }

    /**
     * 如果char为0或空字符，返回默认值
     */
    public static char defaultIfZero(char value, char defaultValue) {
        return (value == 0 || value == '\u0000') ? defaultValue : value;
    }

    // ===== 包装类型处理 =====

    /**
     * 如果Integer为null或0，返回默认值
     */
    public static Integer defaultIfZero(Integer value, Integer defaultValue) {
        return (value == null || value == 0) ? defaultValue : value;
    }

    /**
     * 如果Long为null或0，返回默认值
     */
    public static Long defaultIfZero(Long value, Long defaultValue) {
        return (value == null || value == 0L) ? defaultValue : value;
    }

    /**
     * 如果Float为null或0，返回默认值
     */
    public static Float defaultIfZero(Float value, Float defaultValue) {
        return (value == null || value == 0.0f) ? defaultValue : value;
    }

    /**
     * 如果Double为null或0，返回默认值
     */
    public static Double defaultIfZero(Double value, Double defaultValue) {
        return (value == null || value == 0.0) ? defaultValue : value;
    }

    /**
     * 如果Byte为null或0，返回默认值
     */
    public static Byte defaultIfZero(Byte value, Byte defaultValue) {
        return (value == null || value == 0) ? defaultValue : value;
    }

    /**
     * 如果Short为null或0，返回默认值
     */
    public static Short defaultIfZero(Short value, Short defaultValue) {
        return (value == null || value == 0) ? defaultValue : value;
    }

    /**
     * 如果Character为null或0或空字符，返回默认值
     */
    public static Character defaultIfZero(Character value, Character defaultValue) {
        return (value == null || value == 0 || value == '\u0000') ? defaultValue : value;
    }

    /**
     * 如果BigDecimal为null或0，返回默认值
     */
    public static BigDecimal defaultIfZero(BigDecimal value, BigDecimal defaultValue) {
        return (value == null || BigDecimal.ZERO.compareTo(value) == 0) ? defaultValue : value;
    }

    /**
     * 如果Boolean为null，返回默认值
     */
    public static Boolean defaultIfNull(Boolean value, Boolean defaultValue) {
        return value == null ? defaultValue : value;
    }

    /**
     * 如果boolean为false，返回默认值
     */
    public static boolean defaultIfFalse(boolean value, boolean defaultValue) {
        return !value ? defaultValue : value;
    }

    // ===== 集合类型处理 =====

    /**
     * 如果集合为null或空，返回默认值
     */
    public static <T extends Collection<?>> T defaultIfEmpty(T value, T defaultValue) {
        return (value == null || value.isEmpty()) ? defaultValue : value;
    }

    /**
     * 如果Map为null或空，返回默认值
     */
    public static <T extends Map<?, ?>> T defaultIfEmpty(T value, T defaultValue) {
        return (value == null || value.isEmpty()) ? defaultValue : value;
    }

    /**
     * 如果数组为null或空，返回默认值
     */
    public static <T> T[] defaultIfEmpty(T[] value, T[] defaultValue) {
        return (value == null || value.length == 0) ? defaultValue : value;
    }

    /**
     * 如果基本类型int数组为null或空，返回默认值
     */
    public static int[] defaultIfEmpty(int[] value, int[] defaultValue) {
        return (value == null || value.length == 0) ? defaultValue : value;
    }

    /**
     * 如果基本类型long数组为null或空，返回默认值
     */
    public static long[] defaultIfEmpty(long[] value, long[] defaultValue) {
        return (value == null || value.length == 0) ? defaultValue : value;
    }

    /**
     * 如果基本类型double数组为null或空，返回默认值
     */
    public static double[] defaultIfEmpty(double[] value, double[] defaultValue) {
        return (value == null || value.length == 0) ? defaultValue : value;
    }

    /**
     * 如果基本类型float数组为null或空，返回默认值
     */
    public static float[] defaultIfEmpty(float[] value, float[] defaultValue) {
        return (value == null || value.length == 0) ? defaultValue : value;
    }

    /**
     * 如果基本类型char数组为null或空，返回默认值
     */
    public static char[] defaultIfEmpty(char[] value, char[] defaultValue) {
        return (value == null || value.length == 0) ? defaultValue : value;
    }

    /**
     * 如果基本类型byte数组为null或空，返回默认值
     */
    public static byte[] defaultIfEmpty(byte[] value, byte[] defaultValue) {
        return (value == null || value.length == 0) ? defaultValue : value;
    }

    /**
     * 如果基本类型short数组为null或空，返回默认值
     */
    public static short[] defaultIfEmpty(short[] value, short[] defaultValue) {
        return (value == null || value.length == 0) ? defaultValue : value;
    }

    /**
     * 如果基本类型boolean数组为null或空，返回默认值
     */
    public static boolean[] defaultIfEmpty(boolean[] value, boolean[] defaultValue) {
        return (value == null || value.length == 0) ? defaultValue : value;
    }

    // ===== 通用方法 =====

    /**
     * 通用方法：如果对象为null，返回默认值
     */
    public static <T> T defaultIfNull(T value, T defaultValue) {
        return value == null ? defaultValue : value;
    }

    /**
     * 通用方法：根据类型判断零值并返回默认值
     */
    @SuppressWarnings("unchecked")
    public static <T> T defaultIfZeroValue(T value, T defaultValue) {
        if (value == null) {
            return defaultValue;
        }

        if (value instanceof String && ((String) value).isEmpty()) {
            return defaultValue;
        } else if (value instanceof Integer && (Integer) value == 0) {
            return defaultValue;
        } else if (value instanceof Long && (Long) value == 0L) {
            return defaultValue;
        } else if (value instanceof Double && (Double) value == 0.0) {
            return defaultValue;
        } else if (value instanceof Float && (Float) value == 0.0f) {
            return defaultValue;
        } else if (value instanceof Short && (Short) value == 0) {
            return defaultValue;
        } else if (value instanceof Byte && (Byte) value == 0) {
            return defaultValue;
        } else if (value instanceof Character && ((Character) value == 0 || (Character) value == '\u0000')) {
            return defaultValue;
        } else if (value instanceof BigDecimal && BigDecimal.ZERO.compareTo((BigDecimal) value) == 0) {
            return defaultValue;
        } else if (value instanceof Collection && ((Collection<?>) value).isEmpty()) {
            return defaultValue;
        } else if (value instanceof Map && ((Map<?, ?>) value).isEmpty()) {
            return defaultValue;
        } else if (value.getClass().isArray() && Objects.requireNonNull(java.lang.reflect.Array.getLength(value)) == 0) {
            return defaultValue;
        }

        return value;
    }
}