package com.venus.framework.common.permission;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonPermission implements Serializable {

    @Serial
    private static final long serialVersionUID = 1639377609187353196L;

    /**
     * 权限code
     */
    private String code;

    /**
     * 权限描述
     */
    private String desc;
    /**
     * 父级权限，一般是定义在controller上的权限
     */
    private String parent;
}
