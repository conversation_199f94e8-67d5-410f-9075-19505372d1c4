<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.venus.exchange</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../parent/pom.xml</relativePath>
    </parent>

    <artifactId>framework</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>venus-common</module>
        <module>venus-spring-boot3-starter-mybatis</module>
        <module>venus-spring-boot3-starter-redis</module>
        <module>venus-spring-boot3-starter-job</module>
        <module>venus-spring-boot3-starter-web</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project> 