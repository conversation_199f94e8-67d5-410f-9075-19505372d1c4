package com.venus.framework.mybatis.pagination;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.venus.framework.common.util.DefaultValueUtil;
import org.springframework.util.CollectionUtils;

public class PageUtil {
    public static <T, V extends PaginationParam> Page<T> pageOf(V param) {
        int pageSize = DefaultValueUtil.defaultIfZero(param.getPageSize(), 10);
        int currentPage = DefaultValueUtil.defaultIfZero(param.getCurrentPage(), 1);

        Page<T> page = new Page<>(currentPage, pageSize);
        if (!CollectionUtils.isEmpty(param.getOrderItems())) {
            page.addOrder(param.getOrderItems());
        }
        return page;
    }
}
