package com.venus.framework.mybatis.pagination;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class PaginationResult<V> implements Serializable {
    @Serial
    private static final long serialVersionUID = -610221403143114654L;

    private long total;
    private List<V> list;

    public PaginationResult() {
    }

    public PaginationResult(long total, List<V> list) {
        this.total = total;
        this.list = list;
    }

    public static <V> PaginationResult<V> of(long total, List<V> list) {
        return new PaginationResult<>(total, list);
    }

    public static <V> PaginationResult<V> of(IPage<V> page) {
        return new PaginationResult<>(page.getTotal(), page.getRecords());
    }
}
