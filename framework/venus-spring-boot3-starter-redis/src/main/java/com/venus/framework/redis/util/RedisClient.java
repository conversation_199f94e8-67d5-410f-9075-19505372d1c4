package com.venus.framework.redis.util;

import lombok.RequiredArgsConstructor;
import org.redisson.api.*;
import org.springframework.boot.autoconfigure.AutoConfiguration;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis 工具类
 */
@RequiredArgsConstructor
@AutoConfiguration
public class RedisClient {

    private final RedissonClient redisson;

    /**
     * 获取字符串对象
     */
    public <T> T getString(String key) {
        RBucket<T> bucket = redisson.getBucket(key);
        return bucket.get();
    }

    /**
     * 设置字符串对象
     */
    public <T> void setString(String key, T value) {
        RBucket<T> bucket = redisson.getBucket(key);
        bucket.set(value);
    }

    /**
     * 设置字符串对象并指定过期时间
     */
    public <T> void setString(String key, T value, long timeout, ChronoUnit unit) {
        RBucket<T> bucket = redisson.getBucket(key);
        bucket.set(value, Duration.of(timeout, unit));
    }

    /**
     * 删除字符串对象
     */
    public boolean deleteString(String key) {
        RBucket<Object> bucket = redisson.getBucket(key);
        return bucket.delete();
    }

    /**
     * 获取哈希表
     */
    public <K, V> Map<K, V> getMap(String key) {
        RMap<K, V> map = redisson.getMap(key);
        return map.readAllMap();
    }

    public <K, V> void setMap(String key, Map<K, V> map) {
        RMap<K, V> rMap = redisson.getMap(key);
        rMap.putAll(map);
    }

    public <K, V> void setMap(String key, Map<K, V> map, long timeout, ChronoUnit unit) {
        RMap<K, V> rMap = redisson.getMap(key);
        rMap.putAll(map);
        rMap.expire(Duration.of(timeout, unit));
    }

    public void deleteMap(String key) {
        RMap<Object, Object> map = redisson.getMap(key);
        map.delete();
    }

    /**
     * 设置哈希表字段
     */
    public <K, V> void setMapField(String key, K field, V value) {
        RMap<K, V> map = redisson.getMap(key);
        map.put(field, value);
    }

    /**
     * 获取哈希表字段
     */
    public <K, V> V getMapField(String key, K field) {
        RMap<K, V> map = redisson.getMap(key);
        return map.get(field);
    }

    /**
     * 获取列表
     */
    public <T> List<T> getList(String key) {
        RList<T> list = redisson.getList(key);
        return list.readAll();
    }

    /**
     * 添加列表元素
     */
    public <T> void addListItem(String key, T value) {
        RList<T> list = redisson.getList(key);
        list.add(value);
    }

    /**
     * 获取集合
     */
    public <T> Set<T> getSet(String key) {
        RSet<T> set = redisson.getSet(key);
        return set.readAll();
    }

    /**
     * 添加集合元素
     */
    public <T> boolean addSetItem(String key, T value) {
        RSet<T> set = redisson.getSet(key);
        return set.add(value);
    }

    /**
     * 获取有序集合
     */
    public <T> Collection<T> getSortedSet(String key) {
        RSortedSet<T> sortedSet = redisson.getSortedSet(key);
        return sortedSet.readAll();
    }

    /**
     * 获取分布式锁
     */
    public RLock getLock(String key) {
        return redisson.getLock(key);
    }

    /**
     * 尝试获取锁
     */
    public boolean tryLock(String key) {
        RLock lock = redisson.getLock(key);
        return lock.tryLock();
    }

    /**
     * 尝试获取锁
     */
    public boolean tryLock(String key, long waitTime, long leaseTime, TimeUnit unit) {
        RLock lock = redisson.getLock(key);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 释放锁
     */
    public void unlock(String key) {
        RLock lock = redisson.getLock(key);
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    public void expire(String key, long timeout, ChronoUnit unit) {
        redisson.getBucket(key).expire(Duration.of(timeout, unit));
    }
}