package com.venus.framework.redis.security.session;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.venus.framework.common.enums.DeviceTypeEnum;
import com.venus.framework.common.security.session.IAuthSessionHandler;
import com.venus.framework.common.security.session.Session;
import com.venus.framework.redis.security.constants.RedisSecurityConst;
import com.venus.framework.redis.util.RedisClient;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.temporal.ChronoUnit;
import java.util.Map;

@Component
@ConditionalOnProperty(name = "venus.security.session.handler", havingValue = "redis")
@RequiredArgsConstructor
public class RedisAuthSessionHandler implements IAuthSessionHandler {

    private final RedisClient redis;

    @Override
    public Session getSession(String appName, DeviceTypeEnum deviceType, String sessionId) {
        String sessionKey = getSessionKey(appName, deviceType, sessionId);
        Map<String, Object> session = redis.getMap(sessionKey);
        if (CollectionUtils.isEmpty(session)) {
            return null;
        }
        // 延长会话有效期
        redis.expire(sessionKey, 7, ChronoUnit.DAYS);

        return new ObjectMapper().convertValue(session, Session.class);
    }

    @Override
    public void saveSession(String appName, DeviceTypeEnum deviceType, Session session) {
        Map<String, Object> sessionMap = new ObjectMapper().convertValue(session, new TypeReference<>() {
        });

        String sessionKey = getSessionKey(appName, deviceType, session.getId());
        redis.setMap(sessionKey, sessionMap, 7, ChronoUnit.DAYS);
    }

    @Override
    public void removeSession(String appName, DeviceTypeEnum deviceType, String sessionId) {
        redis.deleteMap(getSessionKey(appName, deviceType, sessionId));
    }

    private String getSessionKey(String appName, DeviceTypeEnum deviceType, String sessionId) {
        return String.format(RedisSecurityConst.AUTH_SESSION_KEY, appName, deviceType.getCode(), sessionId);
    }
}
