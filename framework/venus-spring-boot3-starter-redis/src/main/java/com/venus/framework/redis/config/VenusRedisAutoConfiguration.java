package com.venus.framework.redis.config;

import com.venus.framework.redis.util.RedisClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Import;

/**
 * Venus Redis 自动配置
 */
@AutoConfiguration(after = RedisAutoConfiguration.class)
@Import({RedisClient.class})
public class VenusRedisAutoConfiguration {

}
